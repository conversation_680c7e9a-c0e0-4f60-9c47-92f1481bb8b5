//
//  IsitLocal_App.swift
//  IsitLocal
//
//  Created by <PERSON> on 2025-04-02.
//

import SwiftUI
import CoreData

@main
struct IsitLocal_App: App {
    // Use lazy initialization for persistence controller
    let persistenceController = PersistenceController.shared

    // Use StateObject for view models that need to persist across view updates
    @StateObject private var appState = AppState(isDebugMode: false)

    init() {
        // Track app start for performance monitoring
        PerformanceOptimizer.shared.trackAppStart()

        // Debug: Test API key detection on app startup
        print("🚀 App started - Testing API key detection...")
        if let apiKey = Bundle.main.infoDictionary?["GeminiAPIKey"] as? String {
            print("✅ API key found in Info.plist: \(apiKey.prefix(10))...")
            print("✅ Key length: \(apiKey.count)")
            print("✅ Starts with AIza: \(apiKey.hasPrefix("AIza"))")

            // Test GeminiAPIManager initialization
            let manager = GeminiAPIManager.shared
            print("✅ GeminiAPIManager initialized")
        } else {
            print("❌ No API key found in Info.plist")

            // Debug: Print all available keys
            if let infoDictionary = Bundle.main.infoDictionary {
                print("🔍 Available keys in Info.plist:")
                for key in infoDictionary.keys.sorted() {
                    print("  - \(key)")
                }
            }
        }
    }

    var body: some Scene {
        WindowGroup {
            // Use the new modern content view with beautiful UI
            ModernContentView()
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .environmentObject(appState)
                .onAppear {
                    // Set initialized to true immediately
                    appState.isInitialized = true
                }
        }
    }
}

// Note: Main AppState class is now in AppState.swift
