//
//  IsitLocal_App.swift
//  IsitLocal
//
//  Created by <PERSON> on 2025-04-02.
//

import SwiftUI
import CoreData

@main
struct IsitLocal_App: App {
    // Use lazy initialization for persistence controller
    let persistenceController = PersistenceController.shared

    // Use StateObject for view models that need to persist across view updates
    @StateObject private var appState = AppState(isDebugMode: false)

    init() {
        // Track app start for performance monitoring
        PerformanceOptimizer.shared.trackAppStart()
    }

    var body: some Scene {
        WindowGroup {
            // Use the enhanced content view with new features
            EnhancedContentView()
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .environmentObject(appState)
                .onAppear {
                    // Set initialized to true immediately
                    appState.isInitialized = true
                }
        }
    }
}

// Note: Main AppState class is now in AppState.swift
