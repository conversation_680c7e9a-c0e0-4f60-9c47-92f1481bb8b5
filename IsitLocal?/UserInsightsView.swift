//
//  UserInsightsView.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-08.
//

import SwiftUI
import Charts

struct UserInsightsView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @State private var insights: UserInsights?
    @State private var isLoading = true
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(1.5)
                            .padding()
                    } else if let insights = insights {
                        // Summary card
                        VStack(alignment: .leading, spacing: 15) {
                            Text("Your Shopping Insights")
                                .font(.title)
                                .fontWeight(.bold)
                                .padding(.bottom, 5)
                            
                            HStack {
                                VStack(alignment: .center) {
                                    Text("\(insights.totalScannedItems)")
                                        .font(.system(size: 40, weight: .bold))
                                    Text("Products Scanned")
                                        .font(.caption)
                                        .foregroundColor(.gray)
                                }
                                .frame(maxWidth: .infinity)
                                
                                Divider()
                                    .frame(height: 50)
                                
                                VStack(alignment: .center) {
                                    Text("\(Int(insights.localItemsPercentage))%")
                                        .font(.system(size: 40, weight: .bold))
                                        .foregroundColor(localPercentageColor(insights.localItemsPercentage))
                                    Text("Local Products")
                                        .font(.caption)
                                        .foregroundColor(.gray)
                                }
                                .frame(maxWidth: .infinity)
                            }
                            .padding(.vertical)
                            
                            Divider()
                            
                            VStack(alignment: .leading, spacing: 10) {
                                Text("Most Common Origin:")
                                    .font(.headline)
                                Text(insights.mostCommonOrigin)
                                    .font(.subheadline)
                                
                                Text("Most Common Category:")
                                    .font(.headline)
                                    .padding(.top, 5)
                                Text(insights.mostCommonCategory)
                                    .font(.subheadline)
                            }
                        }
                        .padding()
                        .background(Color.white.opacity(0.9))
                        .clipShape(RoundedRectangle(cornerRadius: 20))
                        .shadow(radius: 5)
                        .padding(.horizontal)
                        
                        // Origin chart
                        VStack(alignment: .leading, spacing: 15) {
                            Text("Products by Origin")
                                .font(.headline)
                                .padding(.bottom, 5)
                            
                            if insights.countryCounts.isEmpty {
                                Text("No data available")
                                    .font(.subheadline)
                                    .foregroundColor(.gray)
                                    .padding()
                            } else {
                                Chart {
                                    ForEach(insights.countryCounts.sorted(by: { $0.value > $1.value }).prefix(5), id: \.key) { country, count in
                                        BarMark(
                                            x: .value("Count", count),
                                            y: .value("Country", country)
                                        )
                                        .foregroundStyle(Color.blue.gradient)
                                    }
                                }
                                .frame(height: 200)
                                .padding(.vertical)
                            }
                        }
                        .padding()
                        .background(Color.white.opacity(0.9))
                        .clipShape(RoundedRectangle(cornerRadius: 20))
                        .shadow(radius: 5)
                        .padding(.horizontal)
                        
                        // Category chart
                        VStack(alignment: .leading, spacing: 15) {
                            Text("Products by Category")
                                .font(.headline)
                                .padding(.bottom, 5)
                            
                            if insights.categoryCounts.isEmpty {
                                Text("No data available")
                                    .font(.subheadline)
                                    .foregroundColor(.gray)
                                    .padding()
                            } else {
                                Chart {
                                    ForEach(insights.categoryCounts.sorted(by: { $0.value > $1.value }).prefix(5), id: \.key) { category, count in
                                        SectorMark(
                                            angle: .value("Count", count),
                                            innerRadius: .ratio(0.5),
                                            angularInset: 1.5
                                        )
                                        .foregroundStyle(by: .value("Category", category))
                                    }
                                }
                                .frame(height: 200)
                                .padding(.vertical)
                            }
                        }
                        .padding()
                        .background(Color.white.opacity(0.9))
                        .clipShape(RoundedRectangle(cornerRadius: 20))
                        .shadow(radius: 5)
                        .padding(.horizontal)
                        
                        // Sustainability impact
                        VStack(alignment: .leading, spacing: 15) {
                            Text("Your Sustainability Impact")
                                .font(.headline)
                                .padding(.bottom, 5)
                            
                            VStack(alignment: .center, spacing: 10) {
                                Image(systemName: "leaf.fill")
                                    .font(.system(size: 40))
                                    .foregroundColor(.green)
                                
                                Text("By choosing local products, you've reduced your carbon footprint by approximately:")
                                    .font(.subheadline)
                                    .multilineTextAlignment(.center)
                                    .padding(.vertical)
                                
                                Text("\(calculateCarbonSavings(insights: insights)) kg CO₂")
                                    .font(.system(size: 30, weight: .bold))
                                    .foregroundColor(.green)
                                
                                Text("That's equivalent to planting \(calculateTreeEquivalent(insights: insights)) trees!")
                                    .font(.subheadline)
                                    .padding(.top, 5)
                            }
                            .padding()
                            .frame(maxWidth: .infinity)
                        }
                        .padding()
                        .background(Color.white.opacity(0.9))
                        .clipShape(RoundedRectangle(cornerRadius: 20))
                        .shadow(radius: 5)
                        .padding(.horizontal)
                        
                        // Recommendations
                        VStack(alignment: .leading, spacing: 15) {
                            Text("Recommendations")
                                .font(.headline)
                                .padding(.bottom, 5)
                            
                            VStack(alignment: .leading, spacing: 10) {
                                RecommendationRow(
                                    icon: "arrow.up.circle.fill",
                                    title: "Increase Local Purchases",
                                    description: "Try to find local alternatives for products from \(insights.mostCommonOrigin)."
                                )
                                
                                Divider()
                                
                                RecommendationRow(
                                    icon: "cart.fill",
                                    title: "Explore New Categories",
                                    description: "You frequently buy \(insights.mostCommonCategory). Try exploring local options in other categories."
                                )
                                
                                Divider()
                                
                                RecommendationRow(
                                    icon: "calendar",
                                    title: "Seasonal Shopping",
                                    description: "Buy produce that's in season locally to reduce environmental impact."
                                )
                            }
                        }
                        .padding()
                        .background(Color.white.opacity(0.9))
                        .clipShape(RoundedRectangle(cornerRadius: 20))
                        .shadow(radius: 5)
                        .padding(.horizontal)
                    } else {
                        Text("No insights available yet. Start scanning products!")
                            .font(.headline)
                            .foregroundColor(.gray)
                            .padding()
                    }
                }
                .padding(.vertical)
            }
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.4, green: 0.49, blue: 0.92),  // #667eea
                        Color(red: 0.46, green: 0.29, blue: 0.64)  // #764ba2
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
            )
            .navigationTitle("Your Insights")
            .onAppear {
                loadInsights()
            }
        }
    }
    
    private func loadInsights() {
        isLoading = true
        
        DispatchQueue.global().async {
            let insights = AdvancedDataProcessing.shared.generateUserInsights(context: viewContext)
            
            DispatchQueue.main.async {
                self.insights = insights
                self.isLoading = false
            }
        }
    }
    
    private func localPercentageColor(_ percentage: Double) -> Color {
        if percentage >= 70 {
            return .green
        } else if percentage >= 40 {
            return .yellow
        } else {
            return .orange
        }
    }
    
    private func calculateCarbonSavings(insights: UserInsights) -> Int {
        // Simple calculation: each local product saves roughly 2kg CO2 compared to imported
        let localItems = Int(Double(insights.totalScannedItems) * insights.localItemsPercentage / 100)
        return localItems * 2
    }
    
    private func calculateTreeEquivalent(insights: UserInsights) -> Int {
        // Simple calculation: each tree absorbs roughly 25kg CO2 per year
        let carbonSavings = calculateCarbonSavings(insights: insights)
        return max(1, carbonSavings / 25)
    }
}

struct RecommendationRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 15) {
            Image(systemName: icon)
                .font(.system(size: 24))
                .foregroundColor(.blue)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 5) {
                Text(title)
                    .font(.headline)
                
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
    }
}
