import Foundation

// MARK: - USDA FoodData Central Models
struct USDASearchResponse: Codable {
    let foods: [USDAFood]
    let totalHits: Int
    let currentPage: Int
    let totalPages: Int
}

struct USDAFood: Codable {
    let fdcId: Int
    let description: String
    let brandOwner: String?
    let ingredients: String?
    let foodNutrients: [USDANutrient]?
    let servingSize: Double?
    let servingSizeUnit: String?
    let householdServingFullText: String?
}

struct USDANutrient: Codable {
    let nutrientId: Int
    let nutrientName: String
    let nutrientNumber: String
    let unitName: String
    let value: Double
    let rank: Int?
}

// MARK: - Simplified Nutrition Model for UI
struct NutritionInfo {
    let calories: Double
    let protein: Double
    let carbohydrates: Double
    let fat: Double
    let fiber: Double
    let sugar: Double
    let sodium: Double
    let saturatedFat: Double
    let cholesterol: Double
    let servingSize: String?
    let ingredients: String?
    let allergens: [String]
    let healthScore: Double // 0.0-1.0 for UI compatibility
    let healthGrade: String // A, B, C, D, E
    let warnings: [String]
    let benefits: [String]
}

class NutritionDataManager {
    static let shared = NutritionDataManager()
    private let baseURL = "https://api.nal.usda.gov/fdc/v1"
    private let apiKey = "DEMO_KEY" // Free tier, no key needed for basic usage
    
    private init() {}
    
    // MARK: - Main Function: Get Nutrition Data
    func getNutritionData(for productName: String, barcode: String? = nil) async -> NutritionInfo? {
        print("🍎 Fetching nutrition data for: \(productName)")
        
        // Try multiple search strategies
        if let nutrition = await searchByProductName(productName) {
            return nutrition
        }
        
        if let barcode = barcode, let nutrition = await searchByBarcode(barcode) {
            return nutrition
        }
        
        // Fallback: Generate basic nutrition estimate using AI-like logic
        return generateNutritionEstimate(for: productName)
    }
    
    // MARK: - USDA API Search by Product Name
    private func searchByProductName(_ productName: String) async -> NutritionInfo? {
        let cleanName = cleanProductName(productName)
        let encodedName = cleanName.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? cleanName
        
        let urlString = "\(baseURL)/foods/search?query=\(encodedName)&pageSize=5&api_key=\(apiKey)"
        
        guard let url = URL(string: urlString) else {
            print("❌ Invalid URL for nutrition search")
            return nil
        }
        
        do {
            let (data, _) = try await URLSession.shared.data(from: url)
            let response = try JSONDecoder().decode(USDASearchResponse.self, from: data)
            
            if let bestMatch = findBestMatch(foods: response.foods, searchTerm: cleanName) {
                print("✅ Found nutrition data from USDA")
                return convertToNutritionInfo(bestMatch)
            }
        } catch {
            print("❌ USDA API error: \(error.localizedDescription)")
        }
        
        return nil
    }
    
    // MARK: - Search by Barcode (UPC lookup)
    private func searchByBarcode(_ barcode: String) async -> NutritionInfo? {
        // USDA doesn't directly support barcode lookup, but we can try searching with UPC
        let urlString = "\(baseURL)/foods/search?query=\(barcode)&pageSize=3&api_key=\(apiKey)"
        
        guard let url = URL(string: urlString) else { return nil }
        
        do {
            let (data, _) = try await URLSession.shared.data(from: url)
            let response = try JSONDecoder().decode(USDASearchResponse.self, from: data)
            
            if let food = response.foods.first {
                print("✅ Found nutrition data by barcode")
                return convertToNutritionInfo(food)
            }
        } catch {
            print("❌ Barcode nutrition lookup failed: \(error.localizedDescription)")
        }
        
        return nil
    }
    
    // MARK: - Helper Functions
    private func cleanProductName(_ name: String) -> String {
        return name
            .replacingOccurrences(of: "Unknown Product", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)
            .components(separatedBy: " ")
            .prefix(3) // Take first 3 words for better matching
            .joined(separator: " ")
    }
    
    private func findBestMatch(foods: [USDAFood], searchTerm: String) -> USDAFood? {
        // Score foods based on description similarity
        let scoredFoods = foods.map { food in
            (food: food, score: calculateSimilarity(food.description, searchTerm))
        }
        
        return scoredFoods.max(by: { $0.score < $1.score })?.food
    }
    
    private func calculateSimilarity(_ text1: String, _ text2: String) -> Double {
        let words1 = Set(text1.lowercased().components(separatedBy: .whitespacesAndNewlines))
        let words2 = Set(text2.lowercased().components(separatedBy: .whitespacesAndNewlines))
        
        let intersection = words1.intersection(words2)
        let union = words1.union(words2)
        
        return union.isEmpty ? 0 : Double(intersection.count) / Double(union.count)
    }
    
    private func convertToNutritionInfo(_ food: USDAFood) -> NutritionInfo {
        let nutrients = food.foodNutrients ?? []
        
        // Extract key nutrients by nutrient ID
        let calories = findNutrient(nutrients, ids: [1008, 2047]) // Energy
        let protein = findNutrient(nutrients, ids: [1003]) // Protein
        let carbs = findNutrient(nutrients, ids: [1005]) // Carbohydrates
        let fat = findNutrient(nutrients, ids: [1004]) // Total lipid (fat)
        let fiber = findNutrient(nutrients, ids: [1079]) // Fiber
        let sugar = findNutrient(nutrients, ids: [2000]) // Total sugars
        let sodium = findNutrient(nutrients, ids: [1093]) // Sodium
        
        let servingInfo = formatServingSize(food.servingSize, food.servingSizeUnit, food.householdServingFullText)
        let allergens = detectAllergens(food.ingredients ?? "")
        let healthScore = calculateHealthScore(calories: calories, protein: protein, fiber: fiber, sodium: sodium, sugar: sugar)
        let grade = calculateNutritionGrade(healthScore)
        let (warnings, benefits) = generateHealthInsights(nutrients: nutrients, ingredients: food.ingredients)
        
        return NutritionInfo(
            calories: calories ?? 0,
            protein: protein ?? 0,
            carbohydrates: carbs ?? 0,
            fat: fat ?? 0,
            fiber: fiber ?? 0,
            sugar: sugar ?? 0,
            sodium: sodium ?? 0,
            saturatedFat: findNutrient(nutrients, ids: [1258]) ?? 0, // Saturated fat
            cholesterol: findNutrient(nutrients, ids: [1253]) ?? 0, // Cholesterol
            servingSize: servingInfo,
            ingredients: food.ingredients,
            allergens: allergens,
            healthScore: Double(healthScore) / 100.0, // Convert to 0.0-1.0
            healthGrade: grade,
            warnings: warnings,
            benefits: benefits
        )
    }
    
    private func findNutrient(_ nutrients: [USDANutrient], ids: [Int]) -> Double? {
        for id in ids {
            if let nutrient = nutrients.first(where: { $0.nutrientId == id }) {
                return nutrient.value
            }
        }
        return nil
    }
    
    private func formatServingSize(_ size: Double?, _ unit: String?, _ household: String?) -> String? {
        if let household = household, !household.isEmpty {
            return household
        }
        
        if let size = size, let unit = unit {
            return "\(Int(size))\(unit)"
        }
        
        return nil
    }
    
    private func detectAllergens(_ ingredients: String) -> [String] {
        let allergenKeywords = [
            "milk": "Dairy",
            "egg": "Eggs",
            "peanut": "Peanuts",
            "tree nut": "Tree Nuts",
            "soy": "Soy",
            "wheat": "Wheat",
            "fish": "Fish",
            "shellfish": "Shellfish"
        ]
        
        let lowercased = ingredients.lowercased()
        return allergenKeywords.compactMap { keyword, allergen in
            lowercased.contains(keyword) ? allergen : nil
        }
    }
    
    private func calculateHealthScore(calories: Double?, protein: Double?, fiber: Double?, sodium: Double?, sugar: Double?) -> Int {
        var score = 50 // Base score
        
        // Positive factors
        if let protein = protein, protein > 5 { score += 10 }
        if let fiber = fiber, fiber > 3 { score += 15 }
        
        // Negative factors
        if let sodium = sodium, sodium > 400 { score -= 15 }
        if let sugar = sugar, sugar > 15 { score -= 10 }
        if let calories = calories, calories > 300 { score -= 5 }
        
        return max(0, min(100, score))
    }
    
    private func calculateNutritionGrade(_ score: Int) -> String {
        switch score {
        case 80...100: return "A"
        case 60...79: return "B"
        case 40...59: return "C"
        case 20...39: return "D"
        default: return "E"
        }
    }
    
    private func generateHealthInsights(nutrients: [USDANutrient], ingredients: String?) -> ([String], [String]) {
        var warnings: [String] = []
        var benefits: [String] = []
        
        // Analyze nutrients for insights
        for nutrient in nutrients {
            switch nutrient.nutrientId {
            case 1093 where nutrient.value > 400: // High sodium
                warnings.append("High in sodium (\(Int(nutrient.value))mg)")
            case 2000 where nutrient.value > 15: // High sugar
                warnings.append("High in sugar (\(Int(nutrient.value))g)")
            case 1003 where nutrient.value > 10: // Good protein
                benefits.append("Good source of protein (\(Int(nutrient.value))g)")
            case 1079 where nutrient.value > 5: // High fiber
                benefits.append("High in fiber (\(Int(nutrient.value))g)")
            default:
                break
            }
        }
        
        return (warnings, benefits)
    }
    
    // MARK: - Fallback: Generate Nutrition Estimate
    private func generateNutritionEstimate(for productName: String) -> NutritionInfo {
        print("🤖 Generating nutrition estimate for: \(productName)")
        
        // Basic estimation based on product name keywords
        let name = productName.lowercased()
        var calories: Double = 150
        var protein: Double = 3
        var carbs: Double = 20
        var fat: Double = 5
        
        // Adjust based on product type
        if name.contains("chocolate") || name.contains("candy") {
            calories = 200; carbs = 35; fat = 8; protein = 2
        } else if name.contains("bread") || name.contains("cereal") {
            calories = 120; carbs = 25; fat = 2; protein = 4
        } else if name.contains("milk") || name.contains("yogurt") {
            calories = 100; carbs = 12; fat = 3; protein = 8
        }
        
        return NutritionInfo(
            calories: calories,
            protein: protein,
            carbohydrates: carbs,
            fat: fat,
            fiber: 2,
            sugar: 8,
            sodium: 150,
            saturatedFat: 2,
            cholesterol: 0,
            servingSize: "1 serving",
            ingredients: nil,
            allergens: [],
            healthScore: 0.6, // 60% as 0.0-1.0
            healthGrade: "C",
            warnings: ["Estimated values - scan for accurate data"],
            benefits: []
        )
    }
}
