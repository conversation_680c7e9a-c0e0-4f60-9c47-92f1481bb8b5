<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21701"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="barcode.viewfinder" catalog="system" translatesAutoresizingMaskIntoConstraints="NO" id="Yzd-Yd-Ygc">
                                <rect key="frame" x="121.66666666666669" y="351" width="150" height="150"/>
                                <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="150" id="Ggf-Yd-Ygc"/>
                                    <constraint firstAttribute="height" constant="150" id="Yzd-Yd-Ygc"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Is It Local?" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Yzd-Yd-Ygd">
                                <rect key="frame" x="20" y="521" width="353" height="36"/>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="30"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                        <color key="backgroundColor" red="0.0" green="0.47843137254901963" blue="1.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="Yzd-Yd-Ygc" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="Yzd-Yd-Yga"/>
                            <constraint firstItem="Yzd-Yd-Ygc" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" id="Yzd-Yd-Ygb"/>
                            <constraint firstItem="Yzd-Yd-Ygd" firstAttribute="top" secondItem="Yzd-Yd-Ygc" secondAttribute="bottom" constant="20" id="Yzd-Yd-Yge"/>
                            <constraint firstItem="Yzd-Yd-Ygd" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="20" id="Yzd-Yd-Ygf"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="Yzd-Yd-Ygd" secondAttribute="trailing" constant="20" id="Yzd-Yd-Ygg"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="53" y="375"/>
        </scene>
    </scenes>
    <resources>
        <image name="barcode.viewfinder" catalog="system" width="128" height="115"/>
    </resources>
</document>
