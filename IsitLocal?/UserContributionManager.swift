//
//  UserContributionManager.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-02.
//

import Foundation
import CoreData

class UserContributionManager {
    private let context: NSManagedObjectContext
    
    init(context: NSManagedObjectContext) {
        self.context = context
    }
    
    // Function to submit a user contribution
    func submitContribution(barcode: String, productName: String, origin: String, notes: String) -> UserContribution {
        let contribution = UserContribution(context: context)
        contribution.barcode = barcode
        contribution.productName = productName
        contribution.origin = origin
        contribution.notes = notes
        contribution.submissionDate = Date()
        contribution.status = "Pending"
        
        do {
            try context.save()
            print("Submitted user contribution for \(productName)")
            return contribution
        } catch {
            print("Failed to submit user contribution: \(error)")
            return contribution
        }
    }
    
    // Function to get all user contributions
    func getUserContributions() -> [UserContribution] {
        let fetchRequest: NSFetchRequest<UserContribution> = UserContribution.fetchRequest()
        fetchRequest.sortDescriptors = [NSSortDescriptor(keyPath: \UserContribution.submissionDate, ascending: false)]
        
        do {
            return try context.fetch(fetchRequest)
        } catch {
            print("Failed to fetch user contributions: \(error)")
            return []
        }
    }
    
    // Function to delete a user contribution
    func deleteUserContribution(contribution: UserContribution) {
        context.delete(contribution)
        
        do {
            try context.save()
            print("Deleted user contribution for \(contribution.productName ?? "Unknown")")
        } catch {
            print("Failed to delete user contribution: \(error)")
        }
    }
}
