//
//  EnhancedSustainabilityView.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-08.
//

import SwiftUI
import Charts

struct EnhancedSustainabilityView: View {
    let barcode: String
    let productName: String
    let origin: String
    let category: String?

    @State private var sustainabilityPrediction: SustainabilityPrediction?
    @State private var isLoading = true

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Product info header
                VStack(alignment: .leading, spacing: 10) {
                    Text(productName)
                        .font(.title)
                        .fontWeight(.bold)

                    Text("Origin: \(origin)")
                        .font(.headline)

                    if let category = category {
                        Text("Category: \(category)")
                            .font(.subheadline)
                    }

                    Text("Barcode: \(barcode)")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(Color.white.opacity(0.9))
                .clipShape(RoundedRectangle(cornerRadius: 20))
                .shadow(radius: 5)
                .padding(.horizontal)

                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                        .scaleEffect(1.5)
                        .padding()
                } else if let prediction = sustainabilityPrediction {
                    // Overall sustainability score
                    VStack {
                        Text("Sustainability Score")
                            .font(.headline)
                            .padding(.bottom, 5)

                        ZStack {
                            Circle()
                                .stroke(lineWidth: 20)
                                .opacity(0.3)
                                .foregroundColor(Color.gray)

                            Circle()
                                .trim(from: 0.0, to: CGFloat(min(prediction.overallScore / 100, 1.0)))
                                .stroke(style: StrokeStyle(lineWidth: 20, lineCap: .round, lineJoin: .round))
                                .foregroundColor(scoreColor(for: prediction.overallScore))
                                .rotationEffect(Angle(degrees: 270.0))
                                .animation(.linear, value: prediction.overallScore)

                            VStack {
                                Text("\(Int(prediction.overallScore))")
                                    .font(.system(size: 50))
                                    .fontWeight(.bold)

                                Text("out of 100")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                        .frame(width: 200, height: 200)
                        .padding()

                        Text(prediction.recommendation)
                            .font(.headline)
                            .multilineTextAlignment(.center)
                            .padding()
                            .foregroundColor(.white)
                            .background(scoreColor(for: prediction.overallScore))
                            .clipShape(RoundedRectangle(cornerRadius: 10))
                    }
                    .padding()
                    .background(Color.white.opacity(0.9))
                    .clipShape(RoundedRectangle(cornerRadius: 20))
                    .shadow(radius: 5)
                    .padding(.horizontal)

                    // Detailed impact metrics
                    VStack(alignment: .leading, spacing: 15) {
                        Text("Environmental Impact Breakdown")
                            .font(.headline)
                            .padding(.bottom, 5)

                        // Bar chart for impact metrics
                        Chart {
                            BarMark(
                                x: .value("Impact", "Carbon"),
                                y: .value("Score", prediction.carbonFootprint)
                            )
                            .foregroundStyle(Color.red.opacity(0.7))

                            BarMark(
                                x: .value("Impact", "Water"),
                                y: .value("Score", prediction.waterUsage)
                            )
                            .foregroundStyle(Color.blue.opacity(0.7))

                            BarMark(
                                x: .value("Impact", "Land"),
                                y: .value("Score", prediction.landUse)
                            )
                            .foregroundStyle(Color.green.opacity(0.7))

                            BarMark(
                                x: .value("Impact", "Transport"),
                                y: .value("Score", prediction.transportationImpact)
                            )
                            .foregroundStyle(Color.orange.opacity(0.7))

                            BarMark(
                                x: .value("Impact", "Packaging"),
                                y: .value("Score", prediction.packagingWaste)
                            )
                            .foregroundStyle(Color.purple.opacity(0.7))
                        }
                        .frame(height: 250)
                        .padding(.vertical)

                        Text("Lower values indicate less environmental impact")
                            .font(.caption)
                            .foregroundColor(.gray)
                            .padding(.top, 5)

                        Divider()

                        // Transportation impact explanation
                        VStack(alignment: .leading, spacing: 10) {
                            Text("Transportation Impact")
                                .font(.headline)

                            Text("This product travels approximately \(calculateDistance(from: origin)) to reach you.")
                                .font(.subheadline)

                            Text("Transportation accounts for about \(Int(prediction.transportationImpact))% of this product's environmental impact.")
                                .font(.subheadline)
                        }
                        .padding(.vertical, 5)

                        Divider()

                        // Alternative suggestions
                        VStack(alignment: .leading, spacing: 10) {
                            Text("Sustainable Alternatives")
                                .font(.headline)

                            Text("Look for products with these characteristics:")
                                .font(.subheadline)

                            HStack {
                                Image(systemName: "leaf.fill")
                                    .foregroundColor(.green)
                                Text("Locally produced")
                                    .font(.subheadline)
                            }

                            HStack {
                                Image(systemName: "leaf.fill")
                                    .foregroundColor(.green)
                                Text("Minimal packaging")
                                    .font(.subheadline)
                            }

                            HStack {
                                Image(systemName: "leaf.fill")
                                    .foregroundColor(.green)
                                Text("Organic or sustainably produced")
                                    .font(.subheadline)
                            }
                        }
                        .padding(.vertical, 5)
                    }
                    .padding()
                    .background(Color.white.opacity(0.9))
                    .clipShape(RoundedRectangle(cornerRadius: 20))
                    .shadow(radius: 5)
                    .padding(.horizontal)
                }
            }
            .padding(.vertical)
        }
        .background(
            LinearGradient(gradient: Gradient(colors: [.blue.opacity(0.3), .green.opacity(0.3)]), startPoint: .top, endPoint: .bottom)
                .ignoresSafeArea()
        )
        .navigationTitle("Sustainability Analysis")
        .onAppear {
            loadSustainabilityPrediction()
        }
    }

    private func loadSustainabilityPrediction() {
        isLoading = true

        // Create product info
        let productInfo = ProductInfo(
            name: productName,
            category: category,
            origin: origin,
            barcode: barcode
        )

        // Get prediction
        DispatchQueue.global().async {
            let prediction = SustainabilityPredictor.shared.predictSustainabilityScore(for: productInfo)

            DispatchQueue.main.async {
                self.sustainabilityPrediction = prediction
                self.isLoading = false
            }
        }
    }

    private func scoreColor(for score: Double) -> Color {
        if score >= 80 {
            return .green
        } else if score >= 60 {
            return .yellow
        } else if score >= 40 {
            return .orange
        } else {
            return .red
        }
    }

    private func calculateDistance(from origin: String) -> String {
        // This would use a geographical database to calculate actual distances
        // For now, we'll return a simplified estimate

        // Use a default country instead of creating a new LocationManager instance
        let userCountry = "United States"

        if origin.contains(userCountry) {
            return "less than 500 miles"
        } else if SustainabilityPredictor.shared.isNeighboringCountry(origin, to: userCountry) {
            return "500-1,500 miles"
        } else if SustainabilityPredictor.shared.isSameContinent(origin, as: userCountry) {
            return "1,500-4,000 miles"
        } else {
            return "over 4,000 miles"
        }
    }
}
