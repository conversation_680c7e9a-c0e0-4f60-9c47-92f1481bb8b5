/* 
  Localizable.strings
  IsitLocal?

  Created by <PERSON> on 2025-04-02.
*/

// General
"app_name" = "Is It Local?";
"scan" = "Scan";
"favorites" = "Favorites";
"history" = "History";
"filter" = "Filter";
"done" = "Done";
"cancel" = "Cancel";
"save" = "Save";
"ok" = "OK";
"unknown" = "Unknown";

// Scan View
"scan_product" = "Scan Product";
"scan_barcode" = "Scan a barcode or QR code to check its origin";
"barcode" = "Barcode";
"product_name" = "Product Name";
"manufacturer" = "Manufacturer";
"ingredients" = "Ingredients";
"category" = "Category";
"origin" = "Origin";
"share_result" = "Share Result";
"add_to_favorites" = "Add to Favorites";
"remove_from_favorites" = "Remove from Favorites";
"add_edit_notes" = "Add/Edit Notes";
"price_comparisons" = "Price Comparisons";
"no_price_comparisons" = "No price comparisons available";
"in_stock" = "In Stock";
"out_of_stock" = "Out of Stock";

// Favorites View
"no_favorites" = "No favorites yet. Scan products and add them to your favorites!";

// History View
"scan_history" = "Scan History";
"filter_history" = "Filter History";
"no_history" = "No scan history yet. Start scanning products!";
"scanned" = "Scanned";

// Filter View
"filter_options" = "Filter Options";
"filter_by_country" = "Filter by Country";
"filter_by_date" = "Filter by Date";
"enter_country" = "Enter country (e.g., Canada)";
"select_date" = "Select Date";
"clear_date_filter" = "Clear Date Filter";

// Notes Editor
"edit_notes" = "Edit Notes";
"add_notes_placeholder" = "Add notes (e.g., Bought at Walmart)";

// Location
"location_access_denied" = "Location Access Denied";
"location_access_message" = "Please enable location services in Settings to allow price comparisons based on your location. Alternatively, price comparisons will default to the United States.";

// Errors
"scanning_failed" = "Scanning failed";
"fetch_failed" = "Failed to fetch product";
