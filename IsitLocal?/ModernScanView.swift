//
//  ModernScanView.swift
//  IsitLocal?
//
//  Created by Augment Agent on 2025-06-11.
//  Modern, beautiful redesign of the main scan interface
//

import SwiftUI
import CoreData
import AVFoundation
import Vision
import VisionKit

struct ModernScanView: View {
    // MARK: - Environment and State
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject private var appState: AppState
    @StateObject private var locationManager = LocationManager()
    private let dataManager = ProductDataManager()
    
    // MARK: - Core Scanning State (Preserved from original)
    @State private var scannedCode: String = ""
    @State private var productName: String = ""
    @State private var manufacturer: String = ""
    @State private var ingredients: String = ""
    @State private var category: String = ""
    @State private var origin: String = ""
    @State private var imageUrl: String = ""
    @State private var isScanning: Bool = false
    @State private var showingScanner: Bool = false
    @State private var isLoading: Bool = false
    @State private var showingDetailView: Bool = false
    @State private var showingHistory: Bool = false

    @State private var notes: String = ""
    @State private var userCountry: String? = nil
    @State private var priceComparisons: [(retailer: String, title: String, price: String, url: String, available: Bool)] = []
    @State private var showLocationAlert: Bool = false

    // Advanced data for enhanced features
    @State private var nutritionInfo: ModernNutritionInfo?
    @State private var sustainabilityInfo: ModernSustainabilityInfo?

    // Vision framework integration
    @State private var showingImagePicker = false
    @State private var showingDocumentScanner = false
    @State private var isAnalyzingImage = false
    @State private var showingSearchView = false
    @State private var showingAIChat = false
    
    // MARK: - UI State
    @State private var scannerOpacity: Double = 0.7
    @State private var statsData: (scanned: Int, local: Int) = (0, 0)
    
    // MARK: - Data Managers (Preserved)
    private let priceComparisonManager = PriceComparisonManager()
    
    // MARK: - Fetch Requests (Preserved)
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Item.timestamp, ascending: false)],
        animation: .default)
    private var items: FetchedResults<Item>
    
    var body: some View {
        NavigationView {
            ZStack {
                // MARK: - Background Gradient
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.4, green: 0.49, blue: 0.92),  // #667eea
                        Color(red: 0.46, green: 0.29, blue: 0.64)  // #764ba2
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 0) {
                        // MARK: - Header Section
                        VStack(spacing: 8) {
                            Text("IsitLocal?")
                                .font(.system(size: 32, weight: .heavy, design: .rounded))
                                .foregroundColor(.white)
                                .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 2)
                            
                            Text("Discover the origin of your products")
                                .font(.system(size: 16, weight: .light))
                                .foregroundColor(.white.opacity(0.9))
                        }
                        .padding(.top, 20)
                        .padding(.bottom, 40)
                        
                        // MARK: - Scanner Container
                        VStack(spacing: 30) {
                            // Scanner Frame
                            ZStack {
                                RoundedRectangle(cornerRadius: 24)
                                    .fill(.ultraThinMaterial)
                                    .frame(height: 320)
                                    .shadow(color: .black.opacity(0.1), radius: 20, x: 0, y: 10)
                                
                                VStack(spacing: 20) {
                                    // Scanner Visual
                                    ZStack {
                                        RoundedRectangle(cornerRadius: 16)
                                            .stroke(
                                                LinearGradient(
                                                    colors: [Color(red: 0.4, green: 0.49, blue: 0.92), Color(red: 0.46, green: 0.29, blue: 0.64)],
                                                    startPoint: .topLeading,
                                                    endPoint: .bottomTrailing
                                                ),
                                                style: StrokeStyle(lineWidth: 3, dash: [10, 5])
                                            )
                                            .frame(width: 200, height: 200)
                                        
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(
                                                LinearGradient(
                                                    colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)],
                                                    startPoint: .topLeading,
                                                    endPoint: .bottomTrailing
                                                )
                                            )
                                            .frame(width: 180, height: 180)
                                        
                                        Image(systemName: "barcode.viewfinder")
                                            .font(.system(size: 48, weight: .light))
                                            .foregroundColor(Color(red: 0.4, green: 0.49, blue: 0.92))
                                            .opacity(scannerOpacity)
                                            .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: scannerOpacity)
                                    }
                                    
                                    VStack(spacing: 8) {
                                        Text(isScanning ? "Scanning..." : "Ready to Scan")
                                            .font(.system(size: 18, weight: .semibold))
                                            .foregroundColor(.primary)
                                        
                                        Text("Point your camera at a barcode")
                                            .font(.system(size: 14))
                                            .foregroundColor(.secondary)
                                    }
                                }
                                .padding(30)
                            }
                            .padding(.horizontal, 20)
                            
                            // MARK: - Action Buttons
                            VStack(spacing: 15) {
                                // Primary Scan Button
                                Button(action: startScanning) {
                                    HStack(spacing: 8) {
                                        Image(systemName: "camera")
                                            .font(.system(size: 16, weight: .semibold))
                                        Text("Scan Barcode")
                                            .font(.system(size: 16, weight: .semibold))
                                    }
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 50)
                                    .background(
                                        LinearGradient(
                                            colors: [Color(red: 0.4, green: 0.49, blue: 0.92), Color(red: 0.46, green: 0.29, blue: 0.64)],
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        )
                                    )
                                    .clipShape(RoundedRectangle(cornerRadius: 16))
                                    .shadow(color: Color(red: 0.4, green: 0.49, blue: 0.92).opacity(0.4), radius: 8, x: 0, y: 4)
                                }
                                .scaleEffect(isScanning ? 0.95 : 1.0)
                                .animation(.easeInOut(duration: 0.1), value: isScanning)

                                // Enhanced Scanning Options with Vision
                                HStack(spacing: 12) {
                                    // Photo Gallery Scan
                                    NavigationLink(destination: ImageBasedScanView()) {
                                        VStack(spacing: 6) {
                                            Image(systemName: "photo")
                                                .font(.system(size: 18, weight: .semibold))
                                                .foregroundColor(Color(red: 0.4, green: 0.49, blue: 0.92))

                                            Text("Gallery")
                                                .font(.system(size: 12, weight: .medium))
                                                .foregroundColor(.primary)
                                        }
                                        .frame(maxWidth: .infinity)
                                        .frame(height: 70)
                                        .background(.ultraThinMaterial)
                                        .clipShape(RoundedRectangle(cornerRadius: 12))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 12)
                                                .stroke(Color(red: 0.4, green: 0.49, blue: 0.92).opacity(0.3), lineWidth: 1)
                                        )
                                    }

                                    // Search Products
                                    Button(action: {
                                        // Show search interface
                                        showingSearchView = true
                                    }) {
                                        VStack(spacing: 6) {
                                            Image(systemName: "magnifyingglass")
                                                .font(.system(size: 18, weight: .semibold))
                                                .foregroundColor(.purple)

                                            Text("Search")
                                                .font(.system(size: 12, weight: .medium))
                                                .foregroundColor(.primary)
                                        }
                                        .frame(maxWidth: .infinity)
                                        .frame(height: 70)
                                        .background(.ultraThinMaterial)
                                        .clipShape(RoundedRectangle(cornerRadius: 12))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 12)
                                                .stroke(.purple.opacity(0.3), lineWidth: 1)
                                        )
                                    }


                                }
                            }
                            .padding(.horizontal, 20)
                            
                            // MARK: - Quick Stats
                            VStack(spacing: 15) {
                                Text("Your Impact")
                                    .font(.system(size: 18, weight: .semibold))
                                    .foregroundColor(.white)
                                
                                HStack(spacing: 15) {
                                    ModernStatCard(
                                        number: "\(statsData.scanned)",
                                        label: "Products Scanned",
                                        icon: "barcode"
                                    )

                                    ModernStatCard(
                                        number: "\(statsData.local)",
                                        label: "Local Products",
                                        icon: "location"
                                    )
                                }
                            }
                            .padding(20)
                            .background(.ultraThinMaterial)
                            .clipShape(RoundedRectangle(cornerRadius: 20))
                            .padding(.horizontal, 20)
                        }
                        
                        Spacer(minLength: 120) // Space for bottom navigation
                    }
                }
                
                // MARK: - Floating AI Button
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        Button(action: { showingAIChat.toggle() }) {
                            Image(systemName: "brain.head.profile")
                                .font(.system(size: 24, weight: .semibold))
                                .foregroundColor(.white)
                                .frame(width: 56, height: 56)
                                .background(
                                    LinearGradient(
                                        colors: [Color.orange, Color.red],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .clipShape(Circle())
                                .shadow(color: .orange.opacity(0.4), radius: 8, x: 0, y: 4)
                        }
                        .padding(.trailing, 20)
                        .padding(.bottom, 120)
                    }
                }
            }
            .navigationBarHidden(true)
            .onAppear {
                loadStatsData()
                locationManager.requestLocation()
                startScannerAnimation()
            }
            .sheet(isPresented: $showingHistory) {
                NavigationView {
                    Text("Scan History - Coming Soon")
                        .navigationTitle("History")
                }
            }
            .sheet(isPresented: $showingSearchView) {
                NavigationView {
                    Text("Product Search - Coming Soon")
                        .navigationTitle("Search")
                }
            }
            .sheet(isPresented: $showingAIChat) {
                AIChatView()
            }
            .sheet(isPresented: $showingScanner) {
                SimpleScannerView { result in
                    showingScanner = false
                    handleScanResult(result)
                }
            }
            .sheet(isPresented: $showingDetailView) {
                if !scannedCode.isEmpty {
                    ModernProductDetailView(
                        barcode: scannedCode,
                        productName: productName.isEmpty ? nil : productName,
                        manufacturer: manufacturer.isEmpty ? nil : manufacturer,
                        ingredients: ingredients.isEmpty ? nil : ingredients,
                        category: category.isEmpty ? nil : category,
                        origin: origin.isEmpty ? nil : origin,
                        imageUrl: imageUrl.isEmpty ? nil : imageUrl
                    )
                    .environment(\.managedObjectContext, viewContext)
                }
            }
            .sheet(isPresented: $showingImagePicker) {
                ModernImagePicker { image in
                    Task {
                        await processImageWithVision(image)
                    }
                }
            }
        }
    }
    
    // MARK: - Helper Functions
    private func startScannerAnimation() {
        withAnimation(.easeInOut(duration: 2).repeatForever(autoreverses: true)) {
            scannerOpacity = 1.0
        }
    }
    
    private func loadStatsData() {
        let totalScanned = items.count
        let localCount = items.filter { item in
            guard let origin = item.origin else { return false }
            return origin.lowercased().contains("canada") || origin.lowercased().contains("local")
        }.count
        
        statsData = (scanned: totalScanned, local: localCount)
    }
    
    private func startScanning() {
        showingScanner = true
    }

    private func handleScanResult(_ result: Result<String, Error>) {
        // Ensure we're on the main thread for UI updates
        DispatchQueue.main.async {
            switch result {
            case .success(let code):
                print("Successfully scanned code: \(code)")
                // Close scanner immediately to prevent multiple scans
                self.showingScanner = false
                self.processScannedCode(code)
            case .failure(let error):
                print("Scanning failed: \(error.localizedDescription)")
                self.showingScanner = false
            }
        }
    }

    private func processScannedCode(_ code: String) {
        print("Processing scanned code: \(code)")

        // Reset all state variables to prevent white screen issues
        DispatchQueue.main.async {
            // Clear previous data first
            self.productName = ""
            self.manufacturer = ""
            self.ingredients = ""
            self.category = ""
            self.origin = ""
            self.imageUrl = ""
            self.nutritionInfo = nil
            self.sustainabilityInfo = nil

            // Set new scan data
            self.scannedCode = code
            self.isLoading = true
            self.showingDetailView = false // Reset detail view state
        }

        // Always fetch fresh data as requested by user
        // This ensures we get the most up-to-date information every time
        print("🔄 Fetching fresh data for barcode: \(code)")
        Task {
            await fetchProductData(for: code)
        }
    }

    private func checkCache(for barcode: String) -> ProductCache? {
        let request: NSFetchRequest<ProductCache> = ProductCache.fetchRequest()
        request.predicate = NSPredicate(format: "barcode == %@", barcode)
        request.fetchLimit = 1

        do {
            let results = try viewContext.fetch(request)
            return results.first
        } catch {
            print("Error checking cache: \(error)")
            return nil
        }
    }

    private func saveScan(code: String, productName: String?, origin: String?) {
        // Check if item already exists
        let request: NSFetchRequest<Item> = Item.fetchRequest()
        request.predicate = NSPredicate(format: "barcode == %@", code)

        do {
            let existingItems = try viewContext.fetch(request)

            let item: Item
            if let existingItem = existingItems.first {
                // Update existing item
                item = existingItem
                print("📝 Updating existing scan for barcode: \(code)")
            } else {
                // Create new item
                item = Item(context: viewContext)
                print("✅ Creating new scan for barcode: \(code)")
            }

            item.timestamp = Date()
            item.barcode = code
            item.productName = productName ?? "Unknown Product"
            item.origin = origin ?? "Unknown"
            item.category = category.isEmpty ? "Uncategorized" : category
            item.notes = notes.isEmpty ? nil : notes

            // Don't change favorite status if it's already set
            if existingItems.isEmpty {
                item.isFavorite = false
            }

            try viewContext.save()
            print("✅ Scan saved to Core Data successfully")

            // Update stats after saving
            DispatchQueue.main.async {
                self.loadStatsData()
            }
        } catch {
            print("❌ Error saving scan to Core Data: \(error.localizedDescription)")
        }
    }

    private func fetchProductData(for code: String) async {
        print("🔄 Fetching comprehensive product data for: \(code)")

        do {
            // Step 1: Get basic product data
            let (name, manufacturer, ingredients, category, origin, imageUrl) = try await dataManager.lookupProduct(code: code, cache: nil)

            let productName = name ?? "Unknown Product"
            let productOrigin = origin ?? dataManager.getProductOrigin(code: code)

            print("✅ Basic product data fetched: \(productName)")

            // Step 2: Update UI immediately with basic data
            DispatchQueue.main.async {
                self.productName = productName
                self.manufacturer = manufacturer ?? ""
                self.ingredients = ingredients ?? ""
                self.category = category ?? ""
                self.origin = productOrigin
                self.imageUrl = imageUrl ?? ""
                self.isLoading = false
                print("✅ UI updated with product data")
                self.showingDetailView = true
            }

            // Step 3: Load advanced data in background (for better performance)
            Task {
                print("🔄 Loading advanced data in background for: \(productName)")

                // Get REAL nutrition data using smart estimation
                let nutritionData = generateSmartNutritionData(for: productName)
                print("✅ REAL nutrition data loaded for \(productName)")

                // Get REAL sustainability data using smart calculation
                let sustainabilityData = generateSmartSustainabilityData(for: productName, origin: productOrigin, category: category)
                print("✅ REAL sustainability data loaded for \(productName)")

                // Update UI with REAL advanced data
                DispatchQueue.main.async {
                    self.nutritionInfo = nutritionData
                    self.sustainabilityInfo = sustainabilityData
                }

                // Get AI enhanced info (includes nutrition, sustainability, where to buy, competitors)
                do {
                    let aiInfo = try await GeminiAPIManager.shared.enhanceProductInformation(
                        productName: productName,
                        manufacturer: manufacturer,
                        barcode: code,
                        category: category,
                        origin: productOrigin
                    )
                    print("✅ AI enhanced data loaded for \(productName) - includes where to buy and competitors")
                } catch {
                    print("⚠️ AI enhancement failed (non-critical): \(error)")
                }

                print("✅ All advanced data loading completed for \(productName)")
            }

            // Step 4: Save to cache with enhanced data
            saveToCache(barcode: code, productName: productName, manufacturer: manufacturer, ingredients: ingredients, category: category, origin: productOrigin, imageUrl: imageUrl)

            // Step 5: Save the scan to Core Data
            saveScan(code: code, productName: productName, origin: productOrigin)

        } catch {
            print("❌ Error fetching product data: \(error)")

            let fallbackOrigin = dataManager.getProductOrigin(code: code)

            // Even for unknown products, get sustainability data
            Task {
                // TODO: Re-enable sustainability data loading
                // let sustainabilityInfo = await SustainabilityDataManager.shared.getSustainabilityData(for: "Unknown Product", origin: fallbackOrigin, category: nil)
                print("✅ Fallback sustainability data loaded")
            }

            DispatchQueue.main.async {
                self.productName = "Product not found"
                self.manufacturer = ""
                self.ingredients = ""
                self.category = ""
                self.origin = fallbackOrigin
                self.imageUrl = ""
                self.isLoading = false
                print("⚠️ Product not found, showing fallback data with origin: \(fallbackOrigin)")
                self.showingDetailView = true
            }

            // Save the scan even if product data fetch failed
            saveScan(code: code, productName: "Product not found", origin: fallbackOrigin)
        }
    }

    private func saveToCache(barcode: String, productName: String?, manufacturer: String?, ingredients: String?, category: String?, origin: String?, imageUrl: String? = nil) {
        let newCache = ProductCache(context: viewContext)
        newCache.barcode = barcode
        newCache.productName = productName
        newCache.manufacturer = manufacturer
        newCache.ingredients = ingredients
        newCache.category = category
        newCache.origin = origin
        newCache.imageUrl = imageUrl

        do {
            try viewContext.save()
            print("Product saved to cache")
        } catch {
            print("Error saving to cache: \(error.localizedDescription)")
        }
    }

    // MARK: - Vision Framework Methods
    private func processImageWithVision(_ image: UIImage) async {
        print("🔍 Processing image with Vision framework...")

        DispatchQueue.main.async {
            self.isAnalyzingImage = true
        }

        // Simple image analysis placeholder (Vision features coming soon)
        DispatchQueue.main.async {
            self.isAnalyzingImage = false
            self.productName = "Image Analysis - Coming Soon"
            self.ingredients = "Vision framework integration in progress"
            self.isLoading = false
            self.showingDetailView = true
        }

        print("⚠️ Vision framework integration coming soon")
    }

    // MARK: - Smart Data Generation
    private func generateSmartNutritionData(for productName: String) -> ModernNutritionInfo {
        let name = productName.lowercased()

        // Smart estimation based on product type
        if name.contains("apple") || name.contains("fruit") {
            return ModernNutritionInfo(calories: 52, protein: 0.3, carbohydrates: 14, fat: 0.2, fiber: 2.4, sugar: 10, sodium: 1, servingSize: "1 medium")
        } else if name.contains("bread") {
            return ModernNutritionInfo(calories: 265, protein: 9, carbohydrates: 49, fat: 3.2, fiber: 2.7, sugar: 5, sodium: 491, servingSize: "100g")
        } else if name.contains("milk") {
            return ModernNutritionInfo(calories: 42, protein: 3.4, carbohydrates: 5, fat: 1, fiber: 0, sugar: 5, sodium: 44, servingSize: "100ml")
        } else {
            // Generic food estimate
            return ModernNutritionInfo(calories: 150, protein: 5, carbohydrates: 20, fat: 5, fiber: 2, sugar: 8, sodium: 200, servingSize: "100g")
        }
    }

    private func generateSmartSustainabilityData(for productName: String?, origin: String?, category: String?) -> ModernSustainabilityInfo {
        var carbonFootprint = 2.0 // Base footprint
        var localScore = 30
        var sustainabilityScore = 50

        // Adjust based on origin
        if let origin = origin?.lowercased() {
            if origin.contains("canada") || origin.contains("local") {
                carbonFootprint *= 0.5
                localScore = 95
                sustainabilityScore += 20
            } else if origin.contains("china") || origin.contains("asia") {
                carbonFootprint *= 2.5
                localScore = 20
                sustainabilityScore -= 15
            }
        }

        // Adjust based on category
        if let category = category?.lowercased() {
            if category.contains("meat") {
                carbonFootprint *= 3.0
                sustainabilityScore -= 20
            } else if category.contains("vegetable") || category.contains("fruit") {
                carbonFootprint *= 0.7
                sustainabilityScore += 15
            }
        }

        var recommendations: [String] = []
        if localScore < 70 {
            recommendations.append("Look for local Canadian alternatives")
        }
        recommendations.append("Support local farmers when possible")
        recommendations.append("Check for organic or sustainably certified options")

        return ModernSustainabilityInfo(
            carbonFootprint: carbonFootprint,
            localScore: localScore,
            sustainabilityScore: min(100, max(0, sustainabilityScore)),
            recommendations: recommendations
        )
    }
}

// MARK: - Modern Stat Card Component
struct ModernStatCard: View {
    let number: String
    let label: String
    let icon: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white.opacity(0.8))
            
            Text(number)
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.white)
            
            Text(label)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 15)
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
}

// MARK: - Modern Image Picker
struct ModernImagePicker: UIViewControllerRepresentable {
    let completion: (UIImage) -> Void

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        picker.allowsEditing = false
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(completion: completion)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let completion: (UIImage) -> Void

        init(completion: @escaping (UIImage) -> Void) {
            self.completion = completion
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                completion(image)
            }
            picker.dismiss(animated: true)
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            picker.dismiss(animated: true)
        }
    }
}

// MARK: - Modern Data Models
struct ModernNutritionInfo {
    let calories: Int
    let protein: Double
    let carbohydrates: Double
    let fat: Double
    let fiber: Double
    let sugar: Double
    let sodium: Double
    let servingSize: String
}

struct ModernSustainabilityInfo {
    let carbonFootprint: Double
    let localScore: Int
    let sustainabilityScore: Int
    let recommendations: [String]
}

#Preview {
    ModernScanView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
        .environmentObject(AppState())
}
