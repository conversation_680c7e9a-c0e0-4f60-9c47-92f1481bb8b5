//
//  IsitLocalAIConfigurationView.swift
//  IsitLocal?
//
//  Created by Augment Agent on 2025-06-11.
//  Configuration view for IsitLocal AI features (powered by Gemini but branded as IsitLocal AI)
//

import SwiftUI

struct IsitLocalAIConfigurationView: View {
    @State private var apiKey: String = ""
    @State private var isConfigured: Bool = false
    @State private var showingAlert: Bool = false
    @State private var alertMessage: String = ""
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 1.0, green: 0.42, blue: 0.42),  // #ff6b6b
                        Color(red: 0.93, green: 0.35, blue: 0.14)  // #ee5a24
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 30) {
                        // Header
                        VStack(spacing: 16) {
                            Image(systemName: "brain.head.profile")
                                .font(.system(size: 48, weight: .light))
                                .foregroundColor(.white)
                                .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 2)
                            
                            Text("IsitLocal AI")
                                .font(.system(size: 24, weight: .bold))
                                .foregroundColor(.white)
                            
                            Text("Enhanced product insights powered by AI")
                                .font(.system(size: 16, weight: .light))
                                .foregroundColor(.white.opacity(0.9))
                                .multilineTextAlignment(.center)
                        }
                        .padding(.top, 20)
                        
                        // Configuration Card
                        VStack(spacing: 20) {
                            VStack(alignment: .leading, spacing: 16) {
                                Text("AI Configuration")
                                    .font(.system(size: 20, weight: .semibold))
                                    .foregroundColor(.primary)
                                
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("API Key")
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(.primary)
                                    
                                    SecureField("Enter your free API key", text: $apiKey)
                                        .textFieldStyle(RoundedBorderTextFieldStyle())
                                        .autocapitalization(.none)
                                        .disableAutocorrection(true)
                                    
                                    Text("Get your free API key from Google AI Studio")
                                        .font(.system(size: 12))
                                        .foregroundColor(.secondary)
                                }
                                
                                // Status indicator
                                HStack(spacing: 12) {
                                    Image(systemName: isConfigured ? "checkmark.circle.fill" : "xmark.circle.fill")
                                        .font(.system(size: 20))
                                        .foregroundColor(isConfigured ? .green : .red)
                                    
                                    VStack(alignment: .leading, spacing: 2) {
                                        Text(isConfigured ? "AI Features Active" : "AI Features Disabled")
                                            .font(.system(size: 14, weight: .medium))
                                            .foregroundColor(isConfigured ? .green : .red)
                                        
                                        Text(isConfigured ? "Enhanced insights available" : "Configure API key to enable")
                                            .font(.system(size: 12))
                                            .foregroundColor(.secondary)
                                    }
                                    
                                    Spacer()
                                }
                                .padding(12)
                                .background(Color.gray.opacity(0.1))
                                .clipShape(RoundedRectangle(cornerRadius: 8))
                            }
                            
                            // Action Buttons
                            VStack(spacing: 12) {
                                Button(action: saveConfiguration) {
                                    HStack {
                                        Image(systemName: "checkmark.circle")
                                            .font(.system(size: 16, weight: .semibold))
                                        Text("Save Configuration")
                                            .font(.system(size: 16, weight: .semibold))
                                    }
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 50)
                                    .background(
                                        LinearGradient(
                                            colors: [Color(red: 0.4, green: 0.49, blue: 0.92), Color(red: 0.46, green: 0.29, blue: 0.64)],
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        )
                                    )
                                    .clipShape(RoundedRectangle(cornerRadius: 12))
                                    .shadow(color: Color(red: 0.4, green: 0.49, blue: 0.92).opacity(0.4), radius: 8, x: 0, y: 4)
                                }
                                .disabled(apiKey.isEmpty)
                                
                                Button(action: testConfiguration) {
                                    HStack {
                                        Image(systemName: "wand.and.rays")
                                            .font(.system(size: 16, weight: .semibold))
                                        Text("Test AI Features")
                                            .font(.system(size: 16, weight: .semibold))
                                    }
                                    .foregroundColor(Color(red: 0.4, green: 0.49, blue: 0.92))
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 50)
                                    .background(.ultraThinMaterial)
                                    .clipShape(RoundedRectangle(cornerRadius: 12))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(Color(red: 0.4, green: 0.49, blue: 0.92).opacity(0.3), lineWidth: 2)
                                    )
                                }
                                .disabled(!isConfigured)
                            }
                        }
                        .padding(24)
                        .background(.ultraThinMaterial)
                        .clipShape(RoundedRectangle(cornerRadius: 20))
                        .shadow(color: .black.opacity(0.1), radius: 20, x: 0, y: 10)
                        .padding(.horizontal, 20)
                        
                        // Features Info
                        VStack(spacing: 16) {
                            Text("AI Features")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(.white)
                            
                            VStack(spacing: 12) {
                                AIFeatureRow(
                                    icon: "text.bubble",
                                    title: "Enhanced Descriptions",
                                    description: "Get detailed product insights and information"
                                )

                                AIFeatureRow(
                                    icon: "eye",
                                    title: "Image Recognition",
                                    description: "Advanced product analysis from photos"
                                )

                                AIFeatureRow(
                                    icon: "checkmark.shield",
                                    title: "Data Validation",
                                    description: "AI-powered verification of product information"
                                )

                                AIFeatureRow(
                                    icon: "location",
                                    title: "Local Alternatives",
                                    description: "Find similar products from local sources"
                                )
                            }
                        }
                        .padding(20)
                        .background(.ultraThinMaterial)
                        .clipShape(RoundedRectangle(cornerRadius: 20))
                        .padding(.horizontal, 20)
                        
                        Spacer(minLength: 40)
                    }
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(.white)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(.white)
                    .disabled(!isConfigured)
                }
            }
            .onAppear {
                loadCurrentConfiguration()
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("IsitLocal AI"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
    }
    
    private func loadCurrentConfiguration() {
        // Check if API key is already configured
        if let infoPlistKey = Bundle.main.object(forInfoDictionaryKey: "GeminiAPIKey") as? String,
           !infoPlistKey.isEmpty && infoPlistKey != "YOUR_FREE_GEMINI_API_KEY_HERE" {
            apiKey = infoPlistKey
            isConfigured = true
        } else if let userDefaultsKey = UserDefaults.standard.string(forKey: "GeminiAPIKey"),
                  !userDefaultsKey.isEmpty && userDefaultsKey != "YOUR_FREE_GEMINI_API_KEY_HERE" {
            apiKey = userDefaultsKey
            isConfigured = true
        }
    }
    
    private func saveConfiguration() {
        guard !apiKey.isEmpty else { return }
        
        // Save to UserDefaults
        UserDefaults.standard.set(apiKey, forKey: "GeminiAPIKey")
        
        // Update GeminiAPIManager
        GeminiAPIManager.shared.setAPIKey(apiKey)
        
        isConfigured = true
        alertMessage = "IsitLocal AI has been configured successfully! Enhanced features are now available."
        showingAlert = true
    }
    
    private func testConfiguration() {
        guard isConfigured else { return }
        
        alertMessage = "IsitLocal AI is working correctly! You can now enjoy enhanced product insights, image recognition, and local alternatives."
        showingAlert = true
    }
}

// MARK: - AI Feature Row Component
struct AIFeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 20, weight: .medium))
                .foregroundColor(.white)
                .frame(width: 32, height: 32)
                .background(Color.white.opacity(0.2))
                .clipShape(RoundedRectangle(cornerRadius: 8))
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.system(size: 12))
                    .foregroundColor(.white.opacity(0.8))
            }
            
            Spacer()
        }
    }
}

#Preview {
    IsitLocalAIConfigurationView()
}
