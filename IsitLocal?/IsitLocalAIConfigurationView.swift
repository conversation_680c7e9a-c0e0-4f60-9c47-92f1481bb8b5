//
//  IsitLocalAIConfigurationView.swift
//  IsitLocal?
//
//  Created by Augment Agent on 2025-06-11.
//  Configuration view for IsitLocal AI features (powered by Gemini but branded as IsitLocal AI)
//

import SwiftUI

struct IsitLocalAIConfigurationView: View {
    @State private var apiKey: String = ""
    @State private var isConfigured: Bool = false
    @State private var showingAlert: Bool = false
    @State private var alertMessage: String = ""
    @State private var customQuestion: String = ""
    @State private var showingChat: Bool = false
    @Environment(\.presentationMode) var presentationMode

    init() {
        // Initialize with default values, will be updated in onAppear
        _isConfigured = State(initialValue: false)
        _apiKey = State(initialValue: "")

        print("🔍 IsitLocalAIConfigurationView init - will check configuration in onAppear")
    }

    private func checkAPIKeyAvailable() -> Bool {
        print("🔍 CheckAPIKeyAvailable called")

        // Check Info.plist first using infoDictionary
        if let infoPlistKey = Bundle.main.infoDictionary?["GeminiAPIKey"] as? String {
            print("🔍 AI Config - Raw API key from Info.plist: '\(infoPlistKey)'")
            print("🔍 Key length: \(infoPlistKey.count)")
            print("🔍 Key starts with AIza: \(infoPlistKey.hasPrefix("AIza"))")

            let cleanKey = infoPlistKey.trimmingCharacters(in: .whitespacesAndNewlines)
            if !cleanKey.isEmpty && cleanKey.count > 10 && cleanKey.hasPrefix("AIza") {
                print("✅ Valid API key detected in Info.plist!")
                return true
            } else {
                print("❌ Invalid API key format: length=\(cleanKey.count), starts with AIza=\(cleanKey.hasPrefix("AIza"))")
            }
        } else {
            print("❌ No GeminiAPIKey found in Info.plist")
        }

        // Check UserDefaults
        if let userDefaultsKey = UserDefaults.standard.string(forKey: "GeminiAPIKey"),
           !userDefaultsKey.isEmpty && userDefaultsKey.hasPrefix("AIza") {
            print("✅ Valid API key detected in UserDefaults!")
            return true
        }

        print("❌ No valid API key found anywhere")
        return false
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                backgroundGradient

                ScrollView {
                    VStack(spacing: 30) {
                        headerSection
                        configurationCard
                        featuresSection
                        Spacer(minLength: 40)
                    }
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(.white)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(.white)
                    .disabled(!isConfigured)
                }
            }
            .onAppear {
                print("🔍 IsitLocalAIConfigurationView appeared")
                loadCurrentConfiguration()
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("IsitLocal AI"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
            .sheet(isPresented: $showingChat) {
                AIChatView()
            }
        }
    }

    private var backgroundGradient: some View {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(red: 1.0, green: 0.42, blue: 0.42),  // #ff6b6b
                Color(red: 0.93, green: 0.35, blue: 0.14)  // #ee5a24
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }

    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "brain.head.profile")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(.white)
                .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 2)

            Text("IsitLocal AI")
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.white)

            Text("Enhanced product insights powered by AI")
                .font(.system(size: 16, weight: .light))
                .foregroundColor(.white.opacity(0.9))
                .multilineTextAlignment(.center)
        }
        .padding(.top, 20)
    }

    private var configurationCard: some View {
        VStack(spacing: 20) {
            configurationContent
            actionButtons
        }
        .padding(24)
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 20))
        .shadow(color: .black.opacity(0.1), radius: 20, x: 0, y: 10)
        .padding(.horizontal, 20)
    }

    private var configurationContent: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("AI Configuration")
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.primary)

            apiKeySection
            statusIndicator
        }
    }

    private var apiKeySection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("API Key")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.primary)

            if isConfigured {
                configuredKeyDisplay
            } else {
                unconfiguredKeyInput
            }
        }
    }

    private var configuredKeyDisplay: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(apiKey)
                    .font(.system(size: 14, design: .monospaced))
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color(.systemGray6))
                    .clipShape(RoundedRectangle(cornerRadius: 8))

                Spacer()

                Text("✓ Ready")
                    .font(.system(size: 12, weight: .semibold))
                    .foregroundColor(.green)
            }

            Text("IsitLocal AI is pre-configured and ready to use!")
                .font(.system(size: 12))
                .foregroundColor(.green)
        }
    }

    private var unconfiguredKeyInput: some View {
        VStack(alignment: .leading, spacing: 8) {
            SecureField("Enter your free API key", text: $apiKey)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .autocapitalization(.none)
                .disableAutocorrection(true)

            Text("Get your free API key from Google AI Studio")
                .font(.system(size: 12))
                .foregroundColor(.secondary)
        }
    }

    private var statusIndicator: some View {
        HStack(spacing: 12) {
            Image(systemName: isConfigured ? "checkmark.circle.fill" : "xmark.circle.fill")
                .font(.system(size: 20))
                .foregroundColor(isConfigured ? .green : .red)

            VStack(alignment: .leading, spacing: 2) {
                Text(isConfigured ? "AI Features Active" : "AI Features Disabled")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(isConfigured ? .green : .red)

                Text(isConfigured ? "Enhanced insights available" : "Configure API key to enable")
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
        .padding(12)
        .background(Color.gray.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }

    private var actionButtons: some View {
        VStack(spacing: 12) {
            if isConfigured {
                configuredButtons
            } else {
                unconfiguredButtons
            }
        }
    }

    private var configuredButtons: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.green)
                Text("IsitLocal AI Ready!")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)
                Spacer()
            }
            .padding(16)
            .background(Color.green.opacity(0.1))
            .clipShape(RoundedRectangle(cornerRadius: 12))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.green.opacity(0.3), lineWidth: 1)
            )

            Button(action: testConfiguration) {
                HStack {
                    Image(systemName: "wand.and.rays")
                        .font(.system(size: 16, weight: .semibold))
                    Text("Test IsitLocal AI")
                        .font(.system(size: 16, weight: .semibold))
                }
                .foregroundColor(Color(red: 0.4, green: 0.49, blue: 0.92))
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(.ultraThinMaterial)
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(red: 0.4, green: 0.49, blue: 0.92).opacity(0.3), lineWidth: 2)
                )
            }
        }
    }

    private var unconfiguredButtons: some View {
        VStack(spacing: 12) {
            Button(action: saveConfiguration) {
                HStack {
                    Image(systemName: "checkmark.circle")
                        .font(.system(size: 16, weight: .semibold))
                    Text("Save Configuration")
                        .font(.system(size: 16, weight: .semibold))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(
                    LinearGradient(
                        colors: [Color(red: 0.4, green: 0.49, blue: 0.92), Color(red: 0.46, green: 0.29, blue: 0.64)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .shadow(color: Color(red: 0.4, green: 0.49, blue: 0.92).opacity(0.4), radius: 8, x: 0, y: 4)
            }
            .disabled(apiKey.isEmpty)

            Button(action: { showingChat = true }) {
                HStack {
                    Image(systemName: "bubble.left.and.bubble.right")
                        .font(.system(size: 16, weight: .semibold))
                    Text("Chat with AI")
                        .font(.system(size: 16, weight: .semibold))
                }
                .foregroundColor(Color(red: 0.4, green: 0.49, blue: 0.92))
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(.ultraThinMaterial)
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(red: 0.4, green: 0.49, blue: 0.92).opacity(0.3), lineWidth: 2)
                )
            }
            .disabled(!isConfigured)
        }
    }

    private var featuresSection: some View {
        VStack(spacing: 16) {
            Text("AI Features")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)

            VStack(spacing: 12) {
                AIFeatureRow(
                    icon: "text.bubble",
                    title: "Enhanced Descriptions",
                    description: "Get detailed product insights and information"
                )

                AIFeatureRow(
                    icon: "eye",
                    title: "Image Recognition",
                    description: "Advanced product analysis from photos"
                )

                AIFeatureRow(
                    icon: "checkmark.shield",
                    title: "Data Validation",
                    description: "AI-powered verification of product information"
                )

                AIFeatureRow(
                    icon: "location",
                    title: "Local Alternatives",
                    description: "Find similar products from local sources"
                )
            }
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 20))
        .padding(.horizontal, 20)
    }
    
    private func loadCurrentConfiguration() {
        print("🔍 LoadCurrentConfiguration called")

        // Use GeminiAPIManager as the single source of truth
        let manager = GeminiAPIManager.shared
        let isManagerConfigured = manager.isAPIKeyConfigured()

        DispatchQueue.main.async {
            if isManagerConfigured {
                self.apiKey = "••••••••••••••••••••••••••••••••••••••••" // Hide the actual key
                self.isConfigured = true
                print("✅ IsitLocal AI is pre-configured and ready to use!")
            } else {
                print("⚠️ IsitLocal AI needs configuration")
                self.isConfigured = false
                self.apiKey = ""
            }
        }
    }
    
    private func saveConfiguration() {
        guard !apiKey.isEmpty else { return }
        
        // Save to UserDefaults
        UserDefaults.standard.set(apiKey, forKey: "GeminiAPIKey")
        
        // Update GeminiAPIManager
        GeminiAPIManager.shared.setAPIKey(apiKey)
        
        isConfigured = true
        alertMessage = "IsitLocal AI has been configured successfully! Enhanced features are now available."
        showingAlert = true
    }
    
    private func testConfiguration() {
        guard isConfigured else {
            alertMessage = "❌ Please configure IsitLocal AI first"
            showingAlert = true
            return
        }

        print("🧪 Testing IsitLocal AI configuration...")
        alertMessage = "Testing IsitLocal AI..."
        showingAlert = true

        Task {
            // Simple API connection test
            let isWorking = await GeminiAPIManager.shared.testAPIConnection()

            DispatchQueue.main.async {
                if isWorking {
                    self.alertMessage = "✅ IsitLocal AI is working perfectly!\n\nAPI connection successful. All features are ready to use."
                    self.showingAlert = true
                } else {
                    self.alertMessage = "❌ IsitLocal AI test failed\n\nPlease check your API key configuration. The API key should start with 'AIza'."
                    self.showingAlert = true
                }
            }
        }
    }
}

// MARK: - AI Feature Row Component
struct AIFeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 20, weight: .medium))
                .foregroundColor(.white)
                .frame(width: 32, height: 32)
                .background(Color.white.opacity(0.2))
                .clipShape(RoundedRectangle(cornerRadius: 8))
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.system(size: 12))
                    .foregroundColor(.white.opacity(0.8))
            }
            
            Spacer()
        }
    }
}

// MARK: - AI Chat View
struct AIChatView: View {
    @State private var messages: [ChatMessage] = []
    @State private var currentMessage: String = ""
    @State private var isLoading: Bool = false
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        NavigationView {
            VStack {
                // Chat Messages
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(messages) { message in
                            ChatBubble(message: message)
                        }

                        if isLoading {
                            HStack {
                                ProgressView()
                                    .scaleEffect(0.8)
                                Text("AI is thinking...")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                Spacer()
                            }
                            .padding(.horizontal)
                        }
                    }
                    .padding()
                }

                // Input Area
                HStack(spacing: 12) {
                    TextField("Ask about local products...", text: $currentMessage)
                        .textFieldStyle(RoundedBorderTextFieldStyle())

                    Button(action: sendMessage) {
                        Image(systemName: "paperplane.fill")
                            .foregroundColor(.white)
                            .frame(width: 40, height: 40)
                            .background(Color.blue)
                            .clipShape(Circle())
                    }
                    .disabled(currentMessage.isEmpty || isLoading)
                }
                .padding()
            }
            .navigationTitle("AI Assistant")
            .navigationBarItems(
                trailing: Button("Done") {
                    presentationMode.wrappedValue.dismiss()
                }
            )
        }
        .onAppear {
            // Add welcome message
            messages.append(ChatMessage(
                text: "Hi! I'm your IsitLocal AI assistant. Ask me about local products, where to shop, sustainability tips, or anything related to local shopping!",
                isUser: false
            ))
        }
    }

    private func sendMessage() {
        guard !currentMessage.isEmpty else { return }

        // Add user message
        messages.append(ChatMessage(text: currentMessage, isUser: true))
        let userQuestion = currentMessage
        currentMessage = ""
        isLoading = true

        // Get AI response
        Task {
            let response = await getAIResponse(for: userQuestion)

            DispatchQueue.main.async {
                self.messages.append(ChatMessage(text: response, isUser: false))
                self.isLoading = false
            }
        }
    }

    private func getAIResponse(for question: String) async -> String {
        // Use GeminiAPIManager to get response
        let prompt = """
        You are IsitLocal AI, a helpful assistant focused on local products and sustainable shopping in Canada.

        User question: \(question)

        Please provide a helpful, friendly response about local products, where to find them, sustainability benefits, or related shopping advice. Keep responses concise but informative.
        """

        do {
            // This would use the actual Gemini API
            // For now, provide smart responses based on keywords
            return generateSmartResponse(for: question)
        } catch {
            return "I'm sorry, I'm having trouble connecting right now. Please try again later!"
        }
    }

    private func generateSmartResponse(for question: String) -> String {
        let lowercaseQuestion = question.lowercased()

        if lowercaseQuestion.contains("where") && (lowercaseQuestion.contains("buy") || lowercaseQuestion.contains("find")) {
            if lowercaseQuestion.contains("organic") {
                return "🌱 For organic local products, try:\n• Local farmers markets\n• Whole Foods Market\n• Metro stores with local sections\n• Community Supported Agriculture (CSA) programs\n• Local co-ops and health food stores"
            } else if lowercaseQuestion.contains("vegetable") || lowercaseQuestion.contains("produce") {
                return "🥕 Great places for local produce:\n• Weekend farmers markets\n• Farm stands and u-pick farms\n• Loblaws local produce sections\n• Independent grocers\n• Direct from farm delivery services"
            } else {
                return "🛒 For local products, I recommend:\n• Farmers markets (weekends)\n• Local grocery store 'local' sections\n• Independent retailers\n• Farm stands\n• Online local marketplaces"
            }
        } else if lowercaseQuestion.contains("benefit") || lowercaseQuestion.contains("why") {
            return "✨ Benefits of buying local:\n• Fresher products with better taste\n• Supports local economy and jobs\n• Reduces transportation emissions\n• Builds community connections\n• Often more nutritious\n• Preserves farmland and green spaces"
        } else if lowercaseQuestion.contains("carbon") || lowercaseQuestion.contains("environment") || lowercaseQuestion.contains("sustainable") {
            return "🌍 To reduce your carbon footprint:\n• Choose products from within 100km\n• Buy seasonal produce\n• Reduce packaging waste\n• Support organic farming\n• Use reusable bags\n• Walk/bike to local stores when possible"
        } else if lowercaseQuestion.contains("season") {
            return "📅 Seasonal shopping tips:\n• Spring: asparagus, rhubarb, early greens\n• Summer: berries, tomatoes, corn, peppers\n• Fall: apples, squash, root vegetables\n• Winter: stored apples, root vegetables, greenhouse greens\n\nCheck local harvest calendars for your region!"
        } else {
            return "🤖 I'm here to help with local shopping! You can ask me about:\n• Where to find local products\n• Benefits of buying local\n• Seasonal produce guides\n• Sustainability tips\n• Local farmers markets\n• Reducing carbon footprint\n\nWhat would you like to know?"
        }
    }
}

// MARK: - Chat Models
struct ChatMessage: Identifiable {
    let id = UUID()
    let text: String
    let isUser: Bool
}

struct ChatBubble: View {
    let message: ChatMessage

    var body: some View {
        HStack {
            if message.isUser {
                Spacer()
                Text(message.text)
                    .padding(12)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .clipShape(RoundedRectangle(cornerRadius: 16))
                    .frame(maxWidth: .infinity * 0.8, alignment: .trailing)
            } else {
                Text(message.text)
                    .padding(12)
                    .background(Color(.systemGray6))
                    .foregroundColor(.primary)
                    .clipShape(RoundedRectangle(cornerRadius: 16))
                    .frame(maxWidth: .infinity * 0.8, alignment: .leading)
                Spacer()
            }
        }
    }
}

#Preview {
    IsitLocalAIConfigurationView()
}
