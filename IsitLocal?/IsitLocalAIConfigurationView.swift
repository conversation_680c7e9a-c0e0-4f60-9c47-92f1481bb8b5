//
//  IsitLocalAIConfigurationView.swift
//  IsitLocal?
//
//  Created by Augment Agent on 2025-06-11.
//  Configuration view for IsitLocal AI features (powered by Gemini but branded as IsitLocal AI)
//

import SwiftUI

struct IsitLocalAIConfigurationView: View {
    @State private var apiKey: String = ""
    @State private var isConfigured: Bool = false
    @State private var showingAlert: Bool = false
    @State private var alertMessage: String = ""
    @Environment(\.presentationMode) var presentationMode

    init() {
        // Initialize with immediate check
        _isConfigured = State(initialValue: checkAPIKeyAvailable())
        _apiKey = State(initialValue: checkAPIKeyAvailable() ? "••••••••••••••••••••••••••••••••••••••••" : "")
    }

    private func checkAPIKeyAvailable() -> Bool {
        // Check Info.plist first using infoDictionary
        if let infoPlistKey = Bundle.main.infoDictionary?["GeminiAPIKey"] as? String {
            print("🔍 AI Config - Raw API key from Info.plist: '\(infoPlistKey)'")
            let cleanKey = infoPlistKey.trimmingCharacters(in: .whitespacesAndNewlines)
            if !cleanKey.isEmpty && cleanKey.count > 10 && cleanKey.hasPrefix("AIza") {
                print("✅ Valid API key detected in Info.plist!")
                return true
            } else {
                print("❌ Invalid API key format: length=\(cleanKey.count), starts with AIza=\(cleanKey.hasPrefix("AIza"))")
            }
        } else {
            print("❌ No GeminiAPIKey found in Info.plist")
        }

        // Check UserDefaults
        if let userDefaultsKey = UserDefaults.standard.string(forKey: "GeminiAPIKey"),
           !userDefaultsKey.isEmpty && userDefaultsKey.hasPrefix("AIza") {
            print("✅ Valid API key detected in UserDefaults!")
            return true
        }

        print("❌ No valid API key found anywhere")
        return false
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                backgroundGradient

                ScrollView {
                    VStack(spacing: 30) {
                        headerSection
                        configurationCard
                        featuresSection
                        Spacer(minLength: 40)
                    }
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(.white)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(.white)
                    .disabled(!isConfigured)
                }
            }
            .onAppear {
                loadCurrentConfiguration()
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("IsitLocal AI"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
    }

    private var backgroundGradient: some View {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(red: 1.0, green: 0.42, blue: 0.42),  // #ff6b6b
                Color(red: 0.93, green: 0.35, blue: 0.14)  // #ee5a24
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }

    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "brain.head.profile")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(.white)
                .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 2)

            Text("IsitLocal AI")
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.white)

            Text("Enhanced product insights powered by AI")
                .font(.system(size: 16, weight: .light))
                .foregroundColor(.white.opacity(0.9))
                .multilineTextAlignment(.center)
        }
        .padding(.top, 20)
    }

    private var configurationCard: some View {
        VStack(spacing: 20) {
            configurationContent
            actionButtons
        }
        .padding(24)
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 20))
        .shadow(color: .black.opacity(0.1), radius: 20, x: 0, y: 10)
        .padding(.horizontal, 20)
    }

    private var configurationContent: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("AI Configuration")
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.primary)

            apiKeySection
            statusIndicator
        }
    }

    private var apiKeySection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("API Key")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.primary)

            if isConfigured {
                configuredKeyDisplay
            } else {
                unconfiguredKeyInput
            }
        }
    }

    private var configuredKeyDisplay: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(apiKey)
                    .font(.system(size: 14, design: .monospaced))
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color(.systemGray6))
                    .clipShape(RoundedRectangle(cornerRadius: 8))

                Spacer()

                Text("✓ Ready")
                    .font(.system(size: 12, weight: .semibold))
                    .foregroundColor(.green)
            }

            Text("IsitLocal AI is pre-configured and ready to use!")
                .font(.system(size: 12))
                .foregroundColor(.green)
        }
    }

    private var unconfiguredKeyInput: some View {
        VStack(alignment: .leading, spacing: 8) {
            SecureField("Enter your free API key", text: $apiKey)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .autocapitalization(.none)
                .disableAutocorrection(true)

            Text("Get your free API key from Google AI Studio")
                .font(.system(size: 12))
                .foregroundColor(.secondary)
        }
    }

    private var statusIndicator: some View {
        HStack(spacing: 12) {
            Image(systemName: isConfigured ? "checkmark.circle.fill" : "xmark.circle.fill")
                .font(.system(size: 20))
                .foregroundColor(isConfigured ? .green : .red)

            VStack(alignment: .leading, spacing: 2) {
                Text(isConfigured ? "AI Features Active" : "AI Features Disabled")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(isConfigured ? .green : .red)

                Text(isConfigured ? "Enhanced insights available" : "Configure API key to enable")
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
        .padding(12)
        .background(Color.gray.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }

    private var actionButtons: some View {
        VStack(spacing: 12) {
            if isConfigured {
                configuredButtons
            } else {
                unconfiguredButtons
            }
        }
    }

    private var configuredButtons: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.green)
                Text("IsitLocal AI Ready!")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)
                Spacer()
            }
            .padding(16)
            .background(Color.green.opacity(0.1))
            .clipShape(RoundedRectangle(cornerRadius: 12))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.green.opacity(0.3), lineWidth: 1)
            )

            Button(action: testConfiguration) {
                HStack {
                    Image(systemName: "wand.and.rays")
                        .font(.system(size: 16, weight: .semibold))
                    Text("Test IsitLocal AI")
                        .font(.system(size: 16, weight: .semibold))
                }
                .foregroundColor(Color(red: 0.4, green: 0.49, blue: 0.92))
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(.ultraThinMaterial)
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(red: 0.4, green: 0.49, blue: 0.92).opacity(0.3), lineWidth: 2)
                )
            }
        }
    }

    private var unconfiguredButtons: some View {
        VStack(spacing: 12) {
            Button(action: saveConfiguration) {
                HStack {
                    Image(systemName: "checkmark.circle")
                        .font(.system(size: 16, weight: .semibold))
                    Text("Save Configuration")
                        .font(.system(size: 16, weight: .semibold))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(
                    LinearGradient(
                        colors: [Color(red: 0.4, green: 0.49, blue: 0.92), Color(red: 0.46, green: 0.29, blue: 0.64)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .shadow(color: Color(red: 0.4, green: 0.49, blue: 0.92).opacity(0.4), radius: 8, x: 0, y: 4)
            }
            .disabled(apiKey.isEmpty)

            Button(action: testConfiguration) {
                HStack {
                    Image(systemName: "wand.and.rays")
                        .font(.system(size: 16, weight: .semibold))
                    Text("Test AI Features")
                        .font(.system(size: 16, weight: .semibold))
                }
                .foregroundColor(Color(red: 0.4, green: 0.49, blue: 0.92))
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(.ultraThinMaterial)
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(red: 0.4, green: 0.49, blue: 0.92).opacity(0.3), lineWidth: 2)
                )
            }
            .disabled(!isConfigured)
        }
    }

    private var featuresSection: some View {
        VStack(spacing: 16) {
            Text("AI Features")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)

            VStack(spacing: 12) {
                AIFeatureRow(
                    icon: "text.bubble",
                    title: "Enhanced Descriptions",
                    description: "Get detailed product insights and information"
                )

                AIFeatureRow(
                    icon: "eye",
                    title: "Image Recognition",
                    description: "Advanced product analysis from photos"
                )

                AIFeatureRow(
                    icon: "checkmark.shield",
                    title: "Data Validation",
                    description: "AI-powered verification of product information"
                )

                AIFeatureRow(
                    icon: "location",
                    title: "Local Alternatives",
                    description: "Find similar products from local sources"
                )
            }
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 20))
        .padding(.horizontal, 20)
    }
    
    private func loadCurrentConfiguration() {
        // Check if API key is already configured in Info.plist
        if let infoPlistKey = Bundle.main.infoDictionary?["GeminiAPIKey"] as? String {
            print("🔍 Loading config - Raw API key from Info.plist: '\(infoPlistKey)'")
            let cleanKey = infoPlistKey.trimmingCharacters(in: .whitespacesAndNewlines)
            if !cleanKey.isEmpty && cleanKey.count > 10 && cleanKey.hasPrefix("AIza") {
                apiKey = "••••••••••••••••••••••••••••••••••••••••" // Hide the actual key
                isConfigured = true
                print("✅ IsitLocal AI is pre-configured and ready to use!")
                return
            } else {
                print("❌ Invalid API key in Info.plist: length=\(cleanKey.count)")
            }
        } else {
            print("❌ No GeminiAPIKey in Info.plist")
        }

        // Check UserDefaults as fallback
        if let userDefaultsKey = UserDefaults.standard.string(forKey: "GeminiAPIKey"),
           !userDefaultsKey.isEmpty && userDefaultsKey.hasPrefix("AIza") {
            apiKey = "••••••••••••••••••••••••••••••••••••••••" // Hide the actual key
            isConfigured = true
            print("✅ IsitLocal AI is configured and ready to use!")
            return
        }

        // If no API key found, show configuration needed
        print("⚠️ IsitLocal AI needs configuration")
        isConfigured = false
        apiKey = ""
    }
    
    private func saveConfiguration() {
        guard !apiKey.isEmpty else { return }
        
        // Save to UserDefaults
        UserDefaults.standard.set(apiKey, forKey: "GeminiAPIKey")
        
        // Update GeminiAPIManager
        GeminiAPIManager.shared.setAPIKey(apiKey)
        
        isConfigured = true
        alertMessage = "IsitLocal AI has been configured successfully! Enhanced features are now available."
        showingAlert = true
    }
    
    private func testConfiguration() {
        guard isConfigured else {
            alertMessage = "❌ Please configure IsitLocal AI first"
            showingAlert = true
            return
        }

        print("🧪 Testing IsitLocal AI configuration...")
        alertMessage = "Testing IsitLocal AI..."
        showingAlert = true

        Task {
            // Simple API connection test
            let isWorking = await GeminiAPIManager.shared.testAPIConnection()

            DispatchQueue.main.async {
                if isWorking {
                    self.alertMessage = "✅ IsitLocal AI is working perfectly!\n\nAPI connection successful. All features are ready to use."
                    self.showingAlert = true
                } else {
                    self.alertMessage = "❌ IsitLocal AI test failed\n\nPlease check your API key configuration. The API key should start with 'AIza'."
                    self.showingAlert = true
                }
            }
        }
    }
}

// MARK: - AI Feature Row Component
struct AIFeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 20, weight: .medium))
                .foregroundColor(.white)
                .frame(width: 32, height: 32)
                .background(Color.white.opacity(0.2))
                .clipShape(RoundedRectangle(cornerRadius: 8))
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.system(size: 12))
                    .foregroundColor(.white.opacity(0.8))
            }
            
            Spacer()
        }
    }
}

#Preview {
    IsitLocalAIConfigurationView()
}
