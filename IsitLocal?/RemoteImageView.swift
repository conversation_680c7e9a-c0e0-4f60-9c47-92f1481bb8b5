//
//  RemoteImageView.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-02.
//

import SwiftUI
import UIKit

struct RemoteImageView: View {
    let url: URL?
    let placeholder: String
    let width: CGFloat?
    let height: CGFloat?

    @State private var image: UIImage? = nil
    @State private var isLoading = true

    init(url: URL?, placeholder: String = "photo", width: CGFloat? = nil, height: CGFloat? = nil) {
        self.url = url
        self.placeholder = placeholder
        self.width = width
        self.height = height
    }

    var body: some View {
        ZStack {
            if isLoading {
                ProgressView()
            } else if let image = image {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .clipShape(RoundedRectangle(cornerRadius: 10))
            } else {
                Image(systemName: placeholder)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .foregroundColor(.gray)
            }
        }
        .frame(width: width, height: height)
        .onAppear {
            loadImage()
        }
    }

    private func loadImage() {
        guard let url = url else {
            isLoading = false
            return
        }

        URLSession.shared.dataTask(with: url) { data, response, error in
            if let data = data, let loadedImage = UIImage(data: data) {
                DispatchQueue.main.async {
                    self.image = loadedImage
                    self.isLoading = false
                }
            } else {
                DispatchQueue.main.async {
                    self.isLoading = false
                }
            }
        }.resume()
    }
}
