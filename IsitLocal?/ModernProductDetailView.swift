//
//  ModernProductDetailView.swift
//  IsitLocal?
//
//  Created by Augment Agent on 2025-06-11.
//  Modern, beautiful redesign of the product detail interface
//

import SwiftUI
import CoreData

struct ModernProductDetailView: View {
    // MARK: - Properties (Preserved from original)
    let barcode: String
    let productName: String?
    let manufacturer: String?
    let ingredients: String?
    let category: String?
    let origin: String?
    let imageUrl: String?
    
    // MARK: - Environment and State (Preserved)
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.presentationMode) private var presentationMode
    @State private var showingNutritionView = false
    @State private var showingSustainabilityView = false
    @State private var showingComparisonView = false
    @State private var showingContributionView = false
    @State private var showingAIEnhancedView = false
    @State private var isFavorite = false

    // Advanced data integration
    @State private var nutritionInfo: NutritionInfo?
    @State private var sustainabilityInfo: SustainabilityInfo?
    @State private var aiEnhancedInfo: EnhancedProductInfo?
    @State private var isLoadingAdvancedData = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color(.systemGroupedBackground)
                    .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 0) {
                        // MARK: - Header with Product Image
                        ZStack {
                            // Header Background
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(red: 0.4, green: 0.49, blue: 0.92),
                                    Color(red: 0.46, green: 0.29, blue: 0.64)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                            .frame(height: 200)
                            .clipShape(RoundedRectangle(cornerRadius: 0))
                            
                            VStack {
                                Spacer()
                                
                                // Product Image Container
                                ZStack {
                                    RoundedRectangle(cornerRadius: 20)
                                        .fill(.ultraThinMaterial)
                                        .frame(width: 280, height: 280)
                                        .shadow(color: .black.opacity(0.2), radius: 20, x: 0, y: 10)
                                    
                                    if let imageUrl = imageUrl, let url = URL(string: imageUrl) {
                                        RemoteImageView(url: url, height: 200)
                                            .clipShape(RoundedRectangle(cornerRadius: 16))
                                    } else {
                                        VStack(spacing: 12) {
                                            Image(systemName: "cube.box")
                                                .font(.system(size: 60, weight: .light))
                                                .foregroundColor(.gray.opacity(0.6))
                                            
                                            Text("Product Image")
                                                .font(.system(size: 14, weight: .medium))
                                                .foregroundColor(.gray)
                                        }
                                    }
                                    
                                    // Origin Badge
                                    if let origin = origin {
                                        VStack {
                                            HStack {
                                                Spacer()
                                                
                                                Text(getOriginFlag(origin) + " " + origin)
                                                    .font(.system(size: 12, weight: .semibold))
                                                    .foregroundColor(.white)
                                                    .padding(.horizontal, 12)
                                                    .padding(.vertical, 6)
                                                    .background(
                                                        LinearGradient(
                                                            colors: [Color.green, Color.teal],
                                                            startPoint: .leading,
                                                            endPoint: .trailing
                                                        )
                                                    )
                                                    .clipShape(Capsule())
                                                    .shadow(color: .green.opacity(0.3), radius: 4, x: 0, y: 2)
                                                    .padding(.top, 15)
                                                    .padding(.trailing, 15)
                                            }
                                            Spacer()
                                        }
                                    }
                                }
                            }
                            .padding(.bottom, 140)
                        }
                        
                        // MARK: - Content Section
                        VStack(spacing: 24) {
                            // Product Title and Info
                            VStack(spacing: 16) {
                                VStack(spacing: 8) {
                                    Text(productName ?? "Unknown Product")
                                        .font(.system(size: 28, weight: .bold))
                                        .foregroundColor(.primary)
                                        .multilineTextAlignment(.center)
                                    
                                    if let manufacturer = manufacturer {
                                        Text("by \(manufacturer)")
                                            .font(.system(size: 16, weight: .medium))
                                            .foregroundColor(.secondary)
                                    }
                                }
                                
                                // Barcode Info
                                HStack(spacing: 12) {
                                    Image(systemName: "barcode")
                                        .font(.system(size: 18, weight: .medium))
                                        .foregroundColor(.secondary)
                                    
                                    Text(barcode)
                                        .font(.system(size: 16, weight: .medium, design: .monospaced))
                                        .foregroundColor(.primary)
                                    
                                    Spacer()
                                }
                                .padding(12)
                                .background(Color(.systemGray6))
                                .clipShape(RoundedRectangle(cornerRadius: 12))
                            }
                            .padding(.horizontal, 20)
                            .padding(.top, -120)
                            
                            // MARK: - Feature Cards
                            VStack(spacing: 16) {
                                // AI Enhanced Info Card
                                FeatureCard(
                                    icon: "brain.head.profile",
                                    iconColor: .orange,
                                    title: "IsitLocal AI Enhanced Info",
                                    subtitle: "Powered by advanced AI",
                                    description: "Get detailed insights, local alternatives, and enhanced product information using AI analysis.",
                                    action: { showingAIEnhancedView.toggle() }
                                )
                                
                                // Nutrition Information Card
                                FeatureCard(
                                    icon: "heart.fill",
                                    iconColor: .pink,
                                    title: "Nutrition Information",
                                    subtitle: "Health insights",
                                    description: "View detailed nutritional information and health recommendations.",
                                    action: { showingNutritionView.toggle() }
                                )
                                
                                // Sustainability Score Card
                                FeatureCard(
                                    icon: "leaf.fill",
                                    iconColor: .green,
                                    title: "Sustainability Score",
                                    subtitle: "Environmental impact",
                                    description: "Learn about the environmental impact and sustainability of this product.",
                                    action: { showingSustainabilityView.toggle() }
                                )
                                
                                // Compare Products Card
                                FeatureCard(
                                    icon: "scale.3d",
                                    iconColor: .blue,
                                    title: "Compare Products",
                                    subtitle: "Find alternatives",
                                    description: "Compare with similar products and find better alternatives.",
                                    action: { showingComparisonView.toggle() }
                                )
                            }
                            .padding(.horizontal, 20)
                            
                            // MARK: - Product Information Section
                            VStack(alignment: .leading, spacing: 16) {
                                HStack {
                                    Image(systemName: "info.circle")
                                        .font(.system(size: 18, weight: .medium))
                                        .foregroundColor(.blue)
                                    
                                    Text("Product Information")
                                        .font(.system(size: 18, weight: .semibold))
                                        .foregroundColor(.primary)
                                    
                                    Spacer()
                                }
                                
                                VStack(spacing: 12) {
                                    if let category = category {
                                        InfoRow(label: "Category", value: category, icon: "tag")
                                    }
                                    
                                    if let origin = origin {
                                        InfoRow(label: "Origin", value: getOriginFlag(origin) + " " + origin, icon: "location")
                                    }
                                    
                                    if let manufacturer = manufacturer {
                                        InfoRow(label: "Manufacturer", value: manufacturer, icon: "building.2")
                                    }
                                }
                            }
                            .padding(20)
                            .background(Color(.systemGray6))
                            .clipShape(RoundedRectangle(cornerRadius: 16))
                            .padding(.horizontal, 20)
                            
                            Spacer(minLength: 100) // Space for action buttons
                        }
                    }
                }
                
                // MARK: - Action Buttons (Fixed at bottom)
                VStack {
                    Spacer()
                    
                    HStack(spacing: 12) {
                        // Favorite Button
                        Button(action: toggleFavorite) {
                            HStack(spacing: 8) {
                                Image(systemName: isFavorite ? "star.fill" : "star")
                                    .font(.system(size: 16, weight: .semibold))
                                Text(isFavorite ? "Favorited" : "Favorite")
                                    .font(.system(size: 16, weight: .semibold))
                            }
                            .foregroundColor(isFavorite ? .orange : .secondary)
                            .frame(maxWidth: .infinity)
                            .frame(height: 50)
                            .background(Color(.systemGray6))
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color(.systemGray4), lineWidth: 1)
                            )
                        }
                        
                        // Share Button
                        Button(action: shareProduct) {
                            HStack(spacing: 8) {
                                Image(systemName: "square.and.arrow.up")
                                    .font(.system(size: 16, weight: .semibold))
                                Text("Share")
                                    .font(.system(size: 16, weight: .semibold))
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 50)
                            .background(
                                LinearGradient(
                                    colors: [Color(red: 0.4, green: 0.49, blue: 0.92), Color(red: 0.46, green: 0.29, blue: 0.64)],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                            .shadow(color: Color(red: 0.4, green: 0.49, blue: 0.92).opacity(0.4), radius: 8, x: 0, y: 4)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 34)
                    .background(.ultraThinMaterial)
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: { presentationMode.wrappedValue.dismiss() }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.white)
                    }
                }
                
                ToolbarItem(placement: .principal) {
                    Text("Product Details")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                }
            }
            .onAppear {
                checkFavoriteStatus()
                loadAdvancedData()
            }
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $showingNutritionView) {
            NutritionView(barcode: barcode)
                .environment(\.managedObjectContext, viewContext)
        }
        .sheet(isPresented: $showingSustainabilityView) {
            if let origin = origin, let productName = productName {
                SustainabilityView(barcode: barcode, origin: origin, productName: productName)
                    .environment(\.managedObjectContext, viewContext)
            }
        }
        .sheet(isPresented: $showingComparisonView) {
            ProductComparisonView()
                .environment(\.managedObjectContext, viewContext)
        }
        .sheet(isPresented: $showingAIEnhancedView) {
            IsitLocalAIEnhancedView(
                barcode: barcode,
                productName: productName,
                manufacturer: manufacturer,
                origin: origin
            )
        }
    }
    
    // MARK: - Helper Functions
    private func getOriginFlag(_ origin: String) -> String {
        let lowercased = origin.lowercased()
        if lowercased.contains("canada") { return "🇨🇦" }
        if lowercased.contains("usa") || lowercased.contains("united states") { return "🇺🇸" }
        if lowercased.contains("mexico") { return "🇲🇽" }
        if lowercased.contains("france") { return "🇫🇷" }
        if lowercased.contains("germany") { return "🇩🇪" }
        if lowercased.contains("italy") { return "🇮🇹" }
        if lowercased.contains("spain") { return "🇪🇸" }
        if lowercased.contains("uk") || lowercased.contains("united kingdom") { return "🇬🇧" }
        if lowercased.contains("japan") { return "🇯🇵" }
        if lowercased.contains("china") { return "🇨🇳" }
        return "🌍"
    }
    
    private func checkFavoriteStatus() {
        // Check if this product is in favorites
        let request: NSFetchRequest<Item> = Item.fetchRequest()
        request.predicate = NSPredicate(format: "barcode == %@ AND isFavorite == true", barcode)
        
        do {
            let results = try viewContext.fetch(request)
            isFavorite = !results.isEmpty
        } catch {
            print("Error checking favorite status: \(error)")
        }
    }
    
    private func toggleFavorite() {
        // Implementation preserved from original
        let request: NSFetchRequest<Item> = Item.fetchRequest()
        request.predicate = NSPredicate(format: "barcode == %@", barcode)
        
        do {
            let results = try viewContext.fetch(request)
            if let item = results.first {
                item.isFavorite.toggle()
                isFavorite = item.isFavorite
                try viewContext.save()
            }
        } catch {
            print("Error toggling favorite: \(error)")
        }
    }
    
    private func shareProduct() {
        // Implementation for sharing product
        let shareText = "Check out this product: \(productName ?? "Unknown Product") - Scanned with IsitLocal?"
        let activityVC = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(activityVC, animated: true)
        }
    }

    // MARK: - Advanced Data Loading
    private func loadAdvancedData() {
        guard !isLoadingAdvancedData else { return }

        isLoadingAdvancedData = true
        print("🔄 Loading advanced data for: \(productName ?? "Unknown")")

        Task {
            print("🔄 Loading advanced data for product: \(productName ?? "Unknown")")

            // TODO: Re-enable nutrition and sustainability data after fixing compilation
            /*
            // Load nutrition data using the actual product name and barcode
            if let productName = productName {
                let nutrition = await NutritionDataManager.shared.getNutritionData(for: productName, barcode: barcode)

                DispatchQueue.main.async {
                    self.nutritionInfo = nutrition
                    print("✅ Nutrition data loaded for \(productName)")
                }
            }

            // Load sustainability data using actual product info
            let sustainability = await SustainabilityDataManager.shared.getSustainabilityData(
                for: productName ?? "Unknown Product",
                origin: origin,
                category: category
            )

            DispatchQueue.main.async {
                self.sustainabilityInfo = sustainability
                print("✅ Sustainability data loaded for \(productName ?? "Unknown")")
            }
            */

            // Load AI enhanced info (this should work now)
            do {
                let aiInfo = try await GeminiAPIManager.shared.enhanceProductInformation(
                    productName: productName ?? "Unknown Product",
                    manufacturer: manufacturer,
                    barcode: barcode,
                    category: category,
                    origin: origin
                )

                DispatchQueue.main.async {
                    self.aiEnhancedInfo = aiInfo
                    print("✅ AI enhanced data loaded for \(productName ?? "Unknown")")
                }
            } catch {
                print("❌ Failed to load AI enhanced data: \(error)")
            }

            DispatchQueue.main.async {
                self.isLoadingAdvancedData = false
                print("✅ All advanced data loading completed for \(productName ?? "Unknown")")
            }
        }
    }
}

// MARK: - Supporting Components

struct FeatureCard: View {
    let icon: String
    let iconColor: Color
    let title: String
    let subtitle: String
    let description: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 16) {
                HStack(spacing: 12) {
                    // Icon
                    ZStack {
                        RoundedRectangle(cornerRadius: 12)
                            .fill(
                                LinearGradient(
                                    colors: [iconColor, iconColor.opacity(0.8)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 40, height: 40)

                        Image(systemName: icon)
                            .font(.system(size: 20, weight: .semibold))
                            .foregroundColor(.white)
                    }

                    // Content
                    VStack(alignment: .leading, spacing: 2) {
                        Text(title)
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.primary)

                        Text(subtitle)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    // Chevron
                    Image(systemName: "chevron.right")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                }

                if !description.isEmpty {
                    Text(description)
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
            .padding(20)
            .background(Color(.systemBackground))
            .clipShape(RoundedRectangle(cornerRadius: 16))
            .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct InfoRow: View {
    let label: String
    let value: String
    let icon: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.secondary)
                .frame(width: 20)

            Text(label)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.secondary)

            Spacer()

            Text(value)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.primary)
                .multilineTextAlignment(.trailing)
        }
        .padding(.vertical, 8)
    }
}

#Preview {
    ModernProductDetailView(
        barcode: "123456789012",
        productName: "Organic Maple Syrup",
        manufacturer: "Canadian Maple Co.",
        ingredients: "Pure maple syrup",
        category: "Food & Beverages",
        origin: "Canada",
        imageUrl: nil
    )
    .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
