//
//  UserContributionView.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-02.
//

import SwiftUI
import CoreData

struct UserContributionView: View {
    let barcode: String?
    let productName: String?
    
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.presentationMode) var presentationMode
    
    @State private var inputBarcode: String = ""
    @State private var inputProductName: String = ""
    @State private var inputOrigin: String = ""
    @State private var inputNotes: String = ""
    @State private var showingSuccessAlert = false
    @State private var showingErrorAlert = false
    @State private var errorMessage = ""
    
    private var contributionManager: UserContributionManager {
        return UserContributionManager(context: viewContext)
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Product Information")) {
                    TextField("Barcode", text: $inputBarcode)
                        .keyboardType(.numberPad)
                    
                    TextField("Product Name", text: $inputProductName)
                    
                    TextField("Origin Country", text: $inputOrigin)
                }
                
                Section(header: Text("Additional Information")) {
                    TextField("Notes (optional)", text: $inputNotes, axis: .vertical)
                        .lineLimit(5)
                }
                
                Section {
                    Button(action: submitContribution) {
                        Text("Submit Contribution")
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                    }
                    .disabled(inputBarcode.isEmpty || inputProductName.isEmpty || inputOrigin.isEmpty)
                }
            }
            .navigationTitle("Contribute Data")
            .onAppear {
                if let barcode = barcode {
                    inputBarcode = barcode
                }
                if let productName = productName {
                    inputProductName = productName
                }
            }
            .alert(isPresented: $showingSuccessAlert) {
                Alert(
                    title: Text("Thank You!"),
                    message: Text("Your contribution has been submitted and will be reviewed by our team."),
                    dismissButton: .default(Text("OK")) {
                        presentationMode.wrappedValue.dismiss()
                    }
                )
            }
            .alert(isPresented: $showingErrorAlert) {
                Alert(
                    title: Text("Error"),
                    message: Text(errorMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
    }
    
    private func submitContribution() {
        guard !inputBarcode.isEmpty && !inputProductName.isEmpty && !inputOrigin.isEmpty else {
            errorMessage = "Please fill in all required fields."
            showingErrorAlert = true
            return
        }
        
        // Validate barcode format
        if !isValidBarcode(inputBarcode) {
            errorMessage = "Please enter a valid barcode (numbers only)."
            showingErrorAlert = true
            return
        }
        
        // Submit the contribution
        _ = contributionManager.submitContribution(
            barcode: inputBarcode,
            productName: inputProductName,
            origin: inputOrigin,
            notes: inputNotes
        )
        
        showingSuccessAlert = true
    }
    
    private func isValidBarcode(_ barcode: String) -> Bool {
        let barcodeRegex = "^[0-9]{8,14}$"
        let barcodePredicate = NSPredicate(format: "SELF MATCHES %@", barcodeRegex)
        return barcodePredicate.evaluate(with: barcode)
    }
}

struct UserContributionsListView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @State private var contributions: [UserContribution] = []
    @State private var isLoading = true
    
    private var contributionManager: UserContributionManager {
        return UserContributionManager(context: viewContext)
    }
    
    var body: some View {
        NavigationView {
            VStack {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                        .scaleEffect(1.5)
                        .padding()
                } else if contributions.isEmpty {
                    Text("No contributions yet")
                        .font(.headline)
                        .foregroundColor(.gray)
                        .padding()
                } else {
                    List {
                        ForEach(contributions, id: \.barcode) { contribution in
                            VStack(alignment: .leading, spacing: 5) {
                                Text(contribution.productName ?? "Unknown Product")
                                    .font(.headline)
                                Text("Origin: \(contribution.origin ?? "Unknown")")
                                    .font(.subheadline)
                                Text("Barcode: \(contribution.barcode ?? "Unknown")")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                if let notes = contribution.notes, !notes.isEmpty {
                                    Text("Notes: \(notes)")
                                        .font(.caption)
                                        .foregroundColor(.gray)
                                }
                                if let submissionDate = contribution.submissionDate {
                                    Text("Submitted: \(submissionDate, formatter: itemFormatter)")
                                        .font(.caption)
                                        .foregroundColor(.gray)
                                }
                                Text("Status: \(contribution.status ?? "Unknown")")
                                    .font(.caption)
                                    .foregroundColor(statusColor(status: contribution.status ?? ""))
                            }
                            .padding(.vertical, 5)
                        }
                        .onDelete(perform: deleteContributions)
                    }
                }
            }
            .navigationTitle("My Contributions")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    EditButton()
                }
                ToolbarItem(placement: .navigationBarLeading) {
                    NavigationLink(destination: UserContributionView(barcode: nil, productName: nil)) {
                        Image(systemName: "plus")
                    }
                }
            }
            .onAppear {
                loadContributions()
            }
        }
    }
    
    private func loadContributions() {
        isLoading = true
        
        DispatchQueue.main.async {
            self.contributions = self.contributionManager.getUserContributions()
            self.isLoading = false
        }
    }
    
    private func deleteContributions(at offsets: IndexSet) {
        for index in offsets {
            let contribution = contributions[index]
            contributionManager.deleteUserContribution(contribution: contribution)
        }
        
        loadContributions()
    }
    
    private func statusColor(status: String) -> Color {
        switch status {
        case "Approved":
            return .green
        case "Rejected":
            return .red
        case "Pending":
            return .orange
        default:
            return .gray
        }
    }
}

private let itemFormatter: DateFormatter = {
    let formatter = DateFormatter()
    formatter.dateStyle = .medium
    formatter.timeStyle = .short
    return formatter
}()
