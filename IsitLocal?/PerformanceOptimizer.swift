//
//  PerformanceOptimizer.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-08.
//

import Foundation
import CoreData
import UIKit

class PerformanceOptimizer {
    // Singleton instance
    static let shared = PerformanceOptimizer()
    
    // Performance metrics
    private var appStartTime: Date?
    private var scanTimes: [TimeInterval] = []
    private var apiCallTimes: [String: [TimeInterval]] = [:]
    private var memoryUsage: [Double] = []
    
    // Cache settings
    private let maxCacheSize: Int = 100 // Maximum number of products to cache
    private let maxImageCacheSize: Int = 50 // Maximum number of images to cache
    private let imageCacheDirectory: URL
    
    // Background task identifier
    private var backgroundTaskID: UIBackgroundTaskIdentifier = .invalid
    
    private init() {
        // Set up image cache directory
        let cacheDirectory = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first!
        imageCacheDirectory = cacheDirectory.appendingPathComponent("productImages", isDirectory: true)
        
        // Create directory if it doesn't exist
        if !FileManager.default.fileExists(atPath: imageCacheDirectory.path) {
            do {
                try FileManager.default.createDirectory(at: imageCacheDirectory, withIntermediateDirectories: true)
            } catch {
                print("Failed to create image cache directory: \(error)")
            }
        }
        
        // Register for memory warning notifications
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    // MARK: - Performance Tracking
    
    // Track app start time
    func trackAppStart() {
        appStartTime = Date()
    }
    
    // Track app fully loaded
    func trackAppLoaded() {
        guard let startTime = appStartTime else { return }
        
        let loadTime = Date().timeIntervalSince(startTime)
        print("App loaded in \(loadTime) seconds")
        
        // Reset start time
        appStartTime = nil
    }
    
    // Track scan performance
    func trackScanStart() -> UUID {
        let scanID = UUID()
        UserDefaults.standard.set(Date().timeIntervalSince1970, forKey: "scan_start_\(scanID.uuidString)")
        return scanID
    }
    
    func trackScanEnd(scanID: UUID) {
        guard let startTime = UserDefaults.standard.object(forKey: "scan_start_\(scanID.uuidString)") as? TimeInterval else {
            return
        }
        
        let scanTime = Date().timeIntervalSince1970 - startTime
        scanTimes.append(scanTime)
        
        // Keep only the last 50 scan times
        if scanTimes.count > 50 {
            scanTimes.removeFirst()
        }
        
        // Remove the start time from UserDefaults
        UserDefaults.standard.removeObject(forKey: "scan_start_\(scanID.uuidString)")
        
        print("Scan completed in \(scanTime) seconds")
    }
    
    // Track API call performance
    func trackAPICallStart(endpoint: String) -> UUID {
        let callID = UUID()
        UserDefaults.standard.set(Date().timeIntervalSince1970, forKey: "api_call_\(callID.uuidString)")
        return callID
    }
    
    func trackAPICallEnd(callID: UUID, endpoint: String) {
        guard let startTime = UserDefaults.standard.object(forKey: "api_call_\(callID.uuidString)") as? TimeInterval else {
            return
        }
        
        let callTime = Date().timeIntervalSince1970 - startTime
        
        if apiCallTimes[endpoint] == nil {
            apiCallTimes[endpoint] = []
        }
        
        apiCallTimes[endpoint]?.append(callTime)
        
        // Keep only the last 50 call times per endpoint
        if let times = apiCallTimes[endpoint], times.count > 50 {
            apiCallTimes[endpoint]?.removeFirst()
        }
        
        // Remove the start time from UserDefaults
        UserDefaults.standard.removeObject(forKey: "api_call_\(callID.uuidString)")
        
        print("API call to \(endpoint) completed in \(callTime) seconds")
    }
    
    // Track memory usage
    func trackMemoryUsage() {
        let memoryUsed = getMemoryUsage()
        memoryUsage.append(memoryUsed)
        
        // Keep only the last 50 memory usage samples
        if memoryUsage.count > 50 {
            memoryUsage.removeFirst()
        }
        
        print("Current memory usage: \(memoryUsed) MB")
    }
    
    // Get current memory usage in MB
    private func getMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: Int(count)) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Double(info.resident_size) / (1024 * 1024)
        } else {
            return 0
        }
    }
    
    // Get performance metrics
    func getPerformanceMetrics() -> PerformanceMetrics {
        let averageScanTime = scanTimes.isEmpty ? 0 : scanTimes.reduce(0, +) / Double(scanTimes.count)
        
        var averageAPITimes: [String: TimeInterval] = [:]
        for (endpoint, times) in apiCallTimes {
            averageAPITimes[endpoint] = times.reduce(0, +) / Double(times.count)
        }
        
        let averageMemoryUsage = memoryUsage.isEmpty ? 0 : memoryUsage.reduce(0, +) / Double(memoryUsage.count)
        
        return PerformanceMetrics(
            averageScanTime: averageScanTime,
            averageAPITimes: averageAPITimes,
            averageMemoryUsage: averageMemoryUsage,
            scanCount: scanTimes.count
        )
    }
    
    // MARK: - Cache Management
    
    // Clean up old cache entries
    func cleanupCache(context: NSManagedObjectContext) {
        // Clean up product cache
        let fetchRequest: NSFetchRequest<ProductCache> = ProductCache.fetchRequest()
        fetchRequest.sortDescriptors = [NSSortDescriptor(keyPath: \ProductCache.lastUpdated, ascending: true)]
        
        do {
            let allCachedProducts = try context.fetch(fetchRequest)
            
            // If we have more than the maximum, delete the oldest ones
            if allCachedProducts.count > maxCacheSize {
                let productsToDelete = allCachedProducts.prefix(allCachedProducts.count - maxCacheSize)
                
                for product in productsToDelete {
                    context.delete(product)
                }
                
                try context.save()
                print("Cleaned up \(productsToDelete.count) old product cache entries")
            }
        } catch {
            print("Failed to clean up product cache: \(error)")
        }
        
        // Clean up image cache
        do {
            let fileManager = FileManager.default
            let imageFiles = try fileManager.contentsOfDirectory(at: imageCacheDirectory, includingPropertiesForKeys: [.creationDateKey], options: [])
            
            if imageFiles.count > maxImageCacheSize {
                // Sort files by creation date
                let sortedFiles = try imageFiles.sorted {
                    let date1 = try $0.resourceValues(forKeys: [.creationDateKey]).creationDate ?? Date.distantPast
                    let date2 = try $1.resourceValues(forKeys: [.creationDateKey]).creationDate ?? Date.distantPast
                    return date1 < date2
                }
                
                // Delete oldest files
                let filesToDelete = sortedFiles.prefix(sortedFiles.count - maxImageCacheSize)
                
                for file in filesToDelete {
                    try fileManager.removeItem(at: file)
                }
                
                print("Cleaned up \(filesToDelete.count) old image cache files")
            }
        } catch {
            print("Failed to clean up image cache: \(error)")
        }
    }
    
    // Cache an image
    func cacheImage(_ image: UIImage, forBarcode barcode: String) {
        guard let data = image.jpegData(compressionQuality: 0.8) else {
            print("Failed to convert image to JPEG data")
            return
        }
        
        let fileURL = imageCacheDirectory.appendingPathComponent("\(barcode).jpg")
        
        do {
            try data.write(to: fileURL)
            print("Cached image for barcode \(barcode)")
        } catch {
            print("Failed to cache image: \(error)")
        }
    }
    
    // Get a cached image
    func getCachedImage(forBarcode barcode: String) -> UIImage? {
        let fileURL = imageCacheDirectory.appendingPathComponent("\(barcode).jpg")
        
        guard FileManager.default.fileExists(atPath: fileURL.path) else {
            return nil
        }
        
        do {
            let data = try Data(contentsOf: fileURL)
            return UIImage(data: data)
        } catch {
            print("Failed to load cached image: \(error)")
            return nil
        }
    }
    
    // MARK: - Background Task Management
    
    // Begin a background task
    func beginBackgroundTask() {
        if backgroundTaskID != .invalid {
            // Already have a background task
            return
        }
        
        backgroundTaskID = UIApplication.shared.beginBackgroundTask {
            // Expiration handler
            self.endBackgroundTask()
        }
    }
    
    // End the background task
    func endBackgroundTask() {
        if backgroundTaskID != .invalid {
            UIApplication.shared.endBackgroundTask(backgroundTaskID)
            backgroundTaskID = .invalid
        }
    }
    
    // MARK: - Memory Management
    
    // Handle memory warning
    @objc private func handleMemoryWarning() {
        print("Memory warning received, cleaning up caches")
        
        // Clear in-memory caches
        URLCache.shared.removeAllCachedResponses()
        
        // Clear image cache
        do {
            let fileManager = FileManager.default
            let imageFiles = try fileManager.contentsOfDirectory(at: imageCacheDirectory, includingPropertiesForKeys: nil, options: [])
            
            for file in imageFiles {
                try fileManager.removeItem(at: file)
            }
            
            print("Cleared image cache")
        } catch {
            print("Failed to clear image cache: \(error)")
        }
    }
}

// Model for performance metrics
struct PerformanceMetrics {
    let averageScanTime: TimeInterval
    let averageAPITimes: [String: TimeInterval]
    let averageMemoryUsage: Double
    let scanCount: Int
}
