//
//  NutritionView.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-02.
//

import SwiftUI
import CoreData

struct NutritionView: View {
    let barcode: String
    @Environment(\.managedObjectContext) private var viewContext
    @State private var nutritionInfo: NutritionInfo?
    @State private var isLoading = true
    @State private var errorMessage: String?

    private let nutritionManager = NutritionManager()

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                        .scaleEffect(1.5)
                        .padding()
                } else if let errorMessage = errorMessage {
                    Text(errorMessage)
                        .font(.headline)
                        .foregroundColor(.red)
                        .padding()
                } else if let nutritionInfo = nutritionInfo {
                    // Nutrition info card
                    VStack(alignment: .leading, spacing: 15) {
                        Text("Nutrition Information")
                            .font(.title)
                            .fontWeight(.bold)
                            .padding(.bottom, 5)

                        NutritionRow(title: "Serving Size", value: nutritionInfo.servingSize ?? "N/A")

                        Divider()

                        NutritionRow(title: "Calories", value: nutritionInfo.calories ?? "N/A")
                        NutritionRow(title: "Fat", value: nutritionInfo.fat ?? "N/A")
                        NutritionRow(title: "Carbohydrates", value: nutritionInfo.carbohydrates ?? "N/A")
                        NutritionRow(title: "Sugar", value: nutritionInfo.sugar ?? "N/A")
                        NutritionRow(title: "Fiber", value: nutritionInfo.fiber ?? "N/A")
                        NutritionRow(title: "Protein", value: nutritionInfo.protein ?? "N/A")
                        NutritionRow(title: "Sodium", value: nutritionInfo.sodium ?? "N/A")

                        Spacer()

                        if let lastUpdated = nutritionInfo.lastUpdated {
                            Text("Last updated: \(lastUpdated, formatter: itemFormatter)")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                    }
                    .padding()
                    .background(Color.white.opacity(0.9))
                    .clipShape(RoundedRectangle(cornerRadius: 20))
                    .shadow(radius: 5)
                    .padding(.horizontal)
                } else {
                    Text("No nutrition information available")
                        .font(.headline)
                        .foregroundColor(.gray)
                        .padding()
                }
            }
            .padding(.vertical)
        }
        .background(
            LinearGradient(gradient: Gradient(colors: [.blue.opacity(0.3), .green.opacity(0.3)]), startPoint: .top, endPoint: .bottom)
                .ignoresSafeArea()
        )
        .navigationTitle("Nutrition Information")
        .onAppear {
            loadNutritionInfo()
        }
    }

    private func loadNutritionInfo() {
        isLoading = true
        errorMessage = nil

        Task {
            let info = await nutritionManager.getNutritionInfo(code: barcode, context: viewContext)
            DispatchQueue.main.async {
                if let info = info {
                    self.nutritionInfo = info
                } else {
                    self.errorMessage = "Failed to load nutrition information"
                }
                self.isLoading = false
            }
        }
    }
}

struct NutritionRow: View {
    let title: String
    let value: String

    var body: some View {
        HStack {
            Text(title)
                .font(.headline)
                .foregroundColor(.primary)
            Spacer()
            Text(value)
                .font(.body)
                .foregroundColor(.secondary)
        }
    }
}

private let itemFormatter: DateFormatter = {
    let formatter = DateFormatter()
    formatter.dateStyle = .medium
    formatter.timeStyle = .none
    return formatter
}()
