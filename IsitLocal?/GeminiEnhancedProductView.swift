//
//  GeminiEnhancedProductView.swift
//  IsitLocal?
//
//  Created by Augment Agent on 2025-06-11.
//  Enhanced product information view using Gemini AI insights
//

import SwiftUI

struct GeminiEnhancedProductView: View {
    let productName: String
    let manufacturer: String?
    let barcode: String
    let category: String?
    let origin: String?
    
    @State private var enhancedInfo: EnhancedProductInfo?
    @State private var localAlternatives: LocalAlternatives?
    @State private var validationResult: ValidationResult?
    @State private var isLoading = false
    @State private var errorMessage: String?
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // Header
                VStack(alignment: .leading, spacing: 10) {
                    Text(productName)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    if let manufacturer = manufacturer {
                        Text("by \(manufacturer)")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("Barcode: \(barcode)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        if let origin = origin {
                            Text(origin)
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.blue.opacity(0.1))
                                .cornerRadius(8)
                        }
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
                
                // Loading indicator
                if isLoading {
                    HStack {
                        ProgressView()
                        Text("Analyzing with Gemini AI...")
                            .font(.subheadline)
                    }
                    .padding()
                }
                
                // Error message
                if let errorMessage = errorMessage {
                    Text(errorMessage)
                        .foregroundColor(.red)
                        .padding()
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(8)
                }
                
                // Enhanced product information
                if let enhancedInfo = enhancedInfo {
                    VStack(alignment: .leading, spacing: 15) {
                        Text("AI-Enhanced Information")
                            .font(.headline)
                            .foregroundColor(.blue)
                        
                        if !enhancedInfo.description.isEmpty {
                            InfoCard(title: "Description", content: enhancedInfo.description, icon: "info.circle")
                        }
                        
                        if let nutrition = enhancedInfo.nutrition, !nutrition.isEmpty {
                            InfoCard(title: "Nutrition Highlights", content: nutrition, icon: "heart.fill")
                        }
                        
                        if let sustainability = enhancedInfo.sustainability, !sustainability.isEmpty {
                            InfoCard(title: "Sustainability", content: sustainability, icon: "leaf.fill")
                        }
                        
                        if let health = enhancedInfo.health, !health.isEmpty {
                            InfoCard(title: "Health Considerations", content: health, icon: "cross.fill")
                        }
                        
                        if let usage = enhancedInfo.usage, !usage.isEmpty {
                            InfoCard(title: "Usage Recommendations", content: usage, icon: "lightbulb.fill")
                        }
                    }
                }
                
                // Data validation results
                if let validation = validationResult {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Data Validation")
                            .font(.headline)
                            .foregroundColor(.green)
                        
                        HStack {
                            Image(systemName: validation.isValid ? "checkmark.circle.fill" : "xmark.circle.fill")
                                .foregroundColor(validation.isValid ? .green : .red)
                            
                            Text(validation.isValid ? "Data appears valid" : "Data may have issues")
                                .font(.subheadline)
                            
                            Spacer()
                            
                            Text("Confidence: \(validation.confidence)/10")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        if let suggestions = validation.suggestions, !suggestions.isEmpty {
                            Text("Suggestions:")
                                .font(.subheadline)
                                .fontWeight(.semibold)
                            Text(suggestions)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        if let inconsistencies = validation.inconsistencies, !inconsistencies.isEmpty {
                            Text("Inconsistencies:")
                                .font(.subheadline)
                                .fontWeight(.semibold)
                                .foregroundColor(.orange)
                            Text(inconsistencies)
                                .font(.caption)
                                .foregroundColor(.orange)
                        }
                    }
                    .padding()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(12)
                }
                
                // Local alternatives
                if let alternatives = localAlternatives {
                    VStack(alignment: .leading, spacing: 15) {
                        Text("Local Alternatives")
                            .font(.headline)
                            .foregroundColor(.purple)
                        
                        if let localBrands = alternatives.localBrands, !localBrands.isEmpty {
                            InfoCard(title: "Local Brands", content: localBrands, icon: "location.fill")
                        }
                        
                        if let similarProducts = alternatives.similarProducts, !similarProducts.isEmpty {
                            InfoCard(title: "Similar Local Products", content: similarProducts, icon: "arrow.2.squarepath")
                        }
                        
                        if let seasonal = alternatives.seasonal, !seasonal.isEmpty {
                            InfoCard(title: "Seasonal Options", content: seasonal, icon: "calendar")
                        }
                        
                        if let benefits = alternatives.benefits, !benefits.isEmpty {
                            InfoCard(title: "Benefits of Local", content: benefits, icon: "star.fill")
                        }
                        
                        if let whereToFind = alternatives.whereToFind, !whereToFind.isEmpty {
                            InfoCard(title: "Where to Find", content: whereToFind, icon: "map.fill")
                        }
                    }
                }
                
                // Refresh button
                Button(action: loadGeminiData) {
                    HStack {
                        Image(systemName: "arrow.clockwise")
                        Text("Refresh AI Analysis")
                    }
                    .font(.subheadline)
                    .foregroundColor(.blue)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(10)
                }
                .disabled(isLoading)
            }
            .padding()
        }
        .navigationTitle("AI Enhanced Info")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            loadGeminiData()
        }
    }
    
    private func loadGeminiData() {
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                // Load enhanced product information
                let enhanced = try await GeminiAPIManager.shared.enhanceProductInformation(
                    productName: productName,
                    manufacturer: manufacturer,
                    barcode: barcode,
                    category: category,
                    origin: origin
                )
                
                // Load validation results
                let validation = try await GeminiAPIManager.shared.validateAndEnhanceProductData(
                    productName: productName,
                    manufacturer: manufacturer,
                    barcode: barcode,
                    origin: origin,
                    category: category
                )
                
                // Load local alternatives
                let alternatives = try await GeminiAPIManager.shared.findLocalAlternatives(
                    productName: productName,
                    category: category,
                    userLocation: "Canada"
                )
                
                DispatchQueue.main.async {
                    self.enhancedInfo = enhanced
                    self.validationResult = validation
                    self.localAlternatives = alternatives
                    self.isLoading = false
                }
                
            } catch {
                DispatchQueue.main.async {
                    self.errorMessage = "Failed to load AI analysis: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
}

struct InfoCard: View {
    let title: String
    let content: String
    let icon: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.blue)
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
            }
            
            Text(content)
                .font(.caption)
                .foregroundColor(.secondary)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding()
        .background(Color.blue.opacity(0.05))
        .cornerRadius(8)
    }
}

#Preview {
    NavigationView {
        GeminiEnhancedProductView(
            productName: "Sample Product",
            manufacturer: "Sample Manufacturer",
            barcode: "123456789012",
            category: "Food",
            origin: "🇨🇦 Canada"
        )
    }
}
