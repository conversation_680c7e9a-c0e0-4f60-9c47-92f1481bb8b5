//
//  ModernFavoritesView.swift
//  IsitLocal?
//
//  Created by Augment Agent on 2025-06-11.
//  Modern, beautiful redesign of the favorites interface
//

import SwiftUI
import CoreData

struct ModernFavoritesView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @State private var searchText = ""
    @State private var selectedFilter = "All"
    @State private var showingDeleteAlert = false
    @State private var itemToDelete: Item?
    
    let filterOptions = ["All", "Local", "Food", "Recent"]
    
    // Fetch favorites
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Item.timestamp, ascending: false)],
        predicate: NSPredicate(format: "isFavorite == true"),
        animation: .default
    ) private var favoriteItems: FetchedResults<Item>
    
    var filteredItems: [Item] {
        var items = Array(favoriteItems)
        
        // Apply search filter
        if !searchText.isEmpty {
            items = items.filter { item in
                (item.productName?.localizedCaseInsensitiveContains(searchText) ?? false) ||
                (item.origin?.localizedCaseInsensitiveContains(searchText) ?? false) ||
                (item.category?.localizedCaseInsensitiveContains(searchText) ?? false)
            }
        }
        
        // Apply category filter
        switch selectedFilter {
        case "Local":
            items = items.filter { item in
                item.origin?.lowercased().contains("canada") == true ||
                item.origin?.lowercased().contains("local") == true
            }
        case "Food":
            items = items.filter { item in
                item.category?.lowercased().contains("food") == true ||
                item.category?.lowercased().contains("beverage") == true
            }
        case "Recent":
            let oneWeekAgo = Calendar.current.date(byAdding: .weekOfYear, value: -1, to: Date()) ?? Date()
            items = items.filter { item in
                item.timestamp ?? Date.distantPast > oneWeekAgo
            }
        default:
            break
        }
        
        return items
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color(.systemGroupedBackground)
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Header
                    ZStack {
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(red: 0.4, green: 0.49, blue: 0.92),
                                Color(red: 0.46, green: 0.29, blue: 0.64)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                        .frame(height: 200)
                        
                        VStack(spacing: 16) {
                            Text("My Favorites")
                                .font(.system(size: 28, weight: .bold))
                                .foregroundColor(.white)
                            
                            Text("Your saved local products")
                                .font(.system(size: 16, weight: .light))
                                .foregroundColor(.white.opacity(0.9))
                            
                            Text("\(favoriteItems.count) favorites")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.white)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 6)
                                .background(Color.white.opacity(0.2))
                                .clipShape(Capsule())
                        }
                        .padding(.top, 40)
                    }
                    
                    VStack(spacing: 0) {
                        // Search Bar
                        VStack(spacing: 16) {
                            HStack {
                                Image(systemName: "magnifyingglass")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.secondary)
                                
                                TextField("Search your favorites...", text: $searchText)
                                    .font(.system(size: 16))
                                
                                if !searchText.isEmpty {
                                    Button(action: { searchText = "" }) {
                                        Image(systemName: "xmark.circle.fill")
                                            .font(.system(size: 16))
                                            .foregroundColor(.secondary)
                                    }
                                }
                            }
                            .padding(16)
                            .background(Color(.systemGray6))
                            .clipShape(RoundedRectangle(cornerRadius: 16))
                            
                            // Filter Tabs
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 12) {
                                    ForEach(filterOptions, id: \.self) { filter in
                                        FilterTab(
                                            title: filter,
                                            isSelected: selectedFilter == filter,
                                            action: { selectedFilter = filter }
                                        )
                                    }
                                }
                                .padding(.horizontal, 20)
                            }
                        }
                        .padding(20)
                        .background(Color(.systemBackground))
                        
                        // Content
                        if filteredItems.isEmpty {
                            EmptyFavoritesView(hasItems: !favoriteItems.isEmpty)
                        } else {
                            ScrollView {
                                LazyVStack(spacing: 16) {
                                    ForEach(filteredItems, id: \.objectID) { item in
                                        FavoriteItemCard(
                                            item: item,
                                            onDelete: { itemToDelete = item; showingDeleteAlert = true }
                                        )
                                    }
                                }
                                .padding(.horizontal, 20)
                                .padding(.top, 20)
                                .padding(.bottom, 120) // Space for bottom navigation
                            }
                        }
                    }
                }
            }
            .navigationBarHidden(true)
            .alert("Remove from Favorites", isPresented: $showingDeleteAlert) {
                Button("Cancel", role: .cancel) { }
                Button("Remove", role: .destructive) {
                    if let item = itemToDelete {
                        removeFromFavorites(item)
                    }
                }
            } message: {
                Text("Are you sure you want to remove this item from your favorites?")
            }
        }
    }
    
    private func removeFromFavorites(_ item: Item) {
        withAnimation {
            item.isFavorite = false
            
            do {
                try viewContext.save()
            } catch {
                print("Error removing from favorites: \(error)")
            }
        }
    }
}

// MARK: - Supporting Views

struct FilterTab: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(isSelected ? .white : .secondary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    isSelected ?
                    LinearGradient(
                        colors: [Color(red: 0.4, green: 0.49, blue: 0.92), Color(red: 0.46, green: 0.29, blue: 0.64)],
                        startPoint: .leading,
                        endPoint: .trailing
                    ) :
                    LinearGradient(colors: [Color(.systemGray6)], startPoint: .leading, endPoint: .trailing)
                )
                .clipShape(Capsule())
                .overlay(
                    Capsule()
                        .stroke(Color(.systemGray4), lineWidth: isSelected ? 0 : 1)
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct FavoriteItemCard: View {
    let item: Item
    let onDelete: () -> Void
    @State private var showingDetail = false
    @State private var cachedProduct: ProductCache?
    @Environment(\.managedObjectContext) private var viewContext
    
    var body: some View {
        Button(action: { showingDetail = true }) {
            VStack(spacing: 16) {
                HStack(spacing: 16) {
                    // Product Image/Icon
                    ZStack {
                        RoundedRectangle(cornerRadius: 12)
                            .fill(
                                LinearGradient(
                                    colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 60, height: 60)
                        
                        if let cachedProduct = cachedProduct,
                           let imageUrl = cachedProduct.imageUrl,
                           let url = URL(string: imageUrl) {
                            RemoteImageView(url: url, height: 50)
                                .clipShape(RoundedRectangle(cornerRadius: 8))
                        } else {
                            Text(getProductEmoji(item.category))
                                .font(.system(size: 24))
                        }
                    }
                    
                    // Product Info
                    VStack(alignment: .leading, spacing: 4) {
                        Text(item.productName ?? "Unknown Product")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.primary)
                            .lineLimit(2)
                        
                        if let cachedProduct = cachedProduct,
                           let manufacturer = cachedProduct.manufacturer {
                            Text("by \(manufacturer)")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.secondary)
                                .lineLimit(1)
                        }
                        
                        // Badges
                        HStack(spacing: 8) {
                            if let origin = item.origin {
                                Badge(text: getOriginFlag(origin) + " " + origin, color: .green)
                            }
                            
                            if let category = item.category {
                                Badge(text: category, color: .gray)
                            }
                        }
                    }
                    
                    Spacer()
                }
                
                // Action Row
                HStack {
                    if let timestamp = item.timestamp {
                        Text("Added \(timeAgoString(from: timestamp))")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    HStack(spacing: 8) {
                        ActionButton(icon: "square.and.arrow.up", color: .blue) {
                            shareProduct(item)
                        }
                        
                        ActionButton(icon: "note.text", color: .purple) {
                            // Add notes functionality
                        }
                        
                        ActionButton(icon: "trash", color: .red) {
                            onDelete()
                        }
                    }
                }
            }
            .padding(20)
            .background(Color(.systemBackground))
            .clipShape(RoundedRectangle(cornerRadius: 16))
            .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
        .onAppear {
            loadCachedProduct()
        }
        .sheet(isPresented: $showingDetail) {
            ModernProductDetailView(
                barcode: item.barcode ?? "",
                productName: item.productName,
                manufacturer: cachedProduct?.manufacturer,
                ingredients: cachedProduct?.ingredients,
                category: item.category,
                origin: item.origin,
                imageUrl: cachedProduct?.imageUrl
            )
        }
    }
    
    private func getProductEmoji(_ category: String?) -> String {
        guard let category = category?.lowercased() else { return "📦" }
        
        if category.contains("food") || category.contains("beverage") { return "🍯" }
        if category.contains("dairy") { return "🧀" }
        if category.contains("bakery") || category.contains("bread") { return "🍞" }
        if category.contains("meat") { return "🥩" }
        if category.contains("fruit") || category.contains("vegetable") { return "🍎" }
        return "📦"
    }
    
    private func getOriginFlag(_ origin: String) -> String {
        let lowercased = origin.lowercased()
        if lowercased.contains("canada") { return "🇨🇦" }
        if lowercased.contains("usa") || lowercased.contains("united states") { return "🇺🇸" }
        if lowercased.contains("mexico") { return "🇲🇽" }
        return "🌍"
    }
    
    private func timeAgoString(from date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
    
    private func loadCachedProduct() {
        guard let barcode = item.barcode else { return }

        let request: NSFetchRequest<ProductCache> = ProductCache.fetchRequest()
        request.predicate = NSPredicate(format: "barcode == %@", barcode)
        request.fetchLimit = 1

        do {
            let results = try viewContext.fetch(request)
            cachedProduct = results.first
        } catch {
            print("Error loading cached product: \(error)")
        }
    }

    private func shareProduct(_ item: Item) {
        let shareText = "Check out this product: \(item.productName ?? "Unknown Product") - Found with IsitLocal?"
        let activityVC = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(activityVC, animated: true)
        }
    }
}

struct Badge: View {
    let text: String
    let color: Color

    var body: some View {
        Text(text)
            .font(.system(size: 12, weight: .medium))
            .foregroundColor(color == .gray ? .secondary : .white)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                Group {
                    if color == .gray {
                        Color(.systemGray5)
                    } else {
                        LinearGradient(
                            colors: [color, color.opacity(0.8)],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    }
                }
            )
            .clipShape(Capsule())
    }
}

struct ActionButton: View {
    let icon: String
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(color)
                .frame(width: 32, height: 32)
                .background(color.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 8))
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct EmptyFavoritesView: View {
    let hasItems: Bool

    var body: some View {
        VStack(spacing: 20) {
            Spacer()

            Image(systemName: hasItems ? "magnifyingglass" : "star")
                .font(.system(size: 60, weight: .light))
                .foregroundColor(.gray.opacity(0.5))

            VStack(spacing: 8) {
                Text(hasItems ? "No results found" : "No favorites yet")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.primary)

                Text(hasItems ? "Try adjusting your search or filters" : "Start scanning products and add them to your favorites")
                    .font(.system(size: 16))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }

            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#Preview {
    ModernFavoritesView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
