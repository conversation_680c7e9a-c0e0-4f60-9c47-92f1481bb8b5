import Foundation

// MARK: - Sustainability Models
struct SustainabilityInfo {
    let carbonFootprint: Double // kg CO2 equivalent
    let waterUsage: Double // liters
    let landUse: Double // m² per kg
    let transportDistance: Double // km
    let packagingScore: Int // 1-10 (10 = most sustainable)
    let seasonalityScore: Int // 1-10 (10 = in season)
    let localScore: Double // 1-10 (10 = very local)
    let overallScore: Double // 1-10 for UI compatibility
    let grade: String // A, B, C, D, E
    let environmentalImpacts: [String]
    let recommendations: [String] // Renamed from sustainabilityTips
    let certifications: [String]
    let alternatives: [String]
}

struct CountryData {
    let name: String
    let continent: String
    let distanceFromUser: Double // km
    let carbonIntensity: Double // kg CO2/km
    let sustainabilityRating: Int // 1-10
}

class SustainabilityDataManager {
    static let shared = SustainabilityDataManager()
    
    // Environmental impact database
    private let carbonFootprintDB: [String: Double] = [
        // Food categories (kg CO2 per kg product)
        "beef": 60.0, "lamb": 24.0, "cheese": 21.0, "chocolate": 18.7,
        "coffee": 16.9, "shrimp": 11.8, "pork": 7.6, "salmon": 6.0,
        "chicken": 6.1, "eggs": 4.5, "rice": 4.0, "milk": 3.2,
        "yogurt": 2.2, "bread": 1.6, "pasta": 1.4, "beans": 0.4,
        "vegetables": 0.4, "fruits": 0.3, "grains": 0.8, "nuts": 0.3,
        
        // Packaged goods estimates
        "snacks": 3.5, "beverages": 0.7, "cereal": 1.2, "cookies": 2.8,
        "candy": 3.2, "chips": 3.8, "soda": 0.5, "juice": 0.8
    ]
    
    private let waterUsageDB: [String: Double] = [
        // Liters per kg
        "beef": 15400, "lamb": 10400, "nuts": 9000, "chocolate": 17000,
        "coffee": 18900, "cheese": 3200, "chicken": 4300, "pork": 6000,
        "eggs": 3300, "milk": 1000, "bread": 1600, "rice": 2500,
        "vegetables": 300, "fruits": 900, "pasta": 1800, "beans": 4000
    ]
    
    private let countryDatabase: [String: CountryData] = [
        "US": CountryData(name: "United States", continent: "North America", distanceFromUser: 0, carbonIntensity: 0.4, sustainabilityRating: 6),
        "CA": CountryData(name: "Canada", continent: "North America", distanceFromUser: 500, carbonIntensity: 0.3, sustainabilityRating: 8),
        "MX": CountryData(name: "Mexico", continent: "North America", distanceFromUser: 2000, carbonIntensity: 0.5, sustainabilityRating: 5),
        "BR": CountryData(name: "Brazil", continent: "South America", distanceFromUser: 8000, carbonIntensity: 0.2, sustainabilityRating: 4),
        "CN": CountryData(name: "China", continent: "Asia", distanceFromUser: 11000, carbonIntensity: 0.7, sustainabilityRating: 3),
        "IN": CountryData(name: "India", continent: "Asia", distanceFromUser: 12000, carbonIntensity: 0.8, sustainabilityRating: 4),
        "DE": CountryData(name: "Germany", continent: "Europe", distanceFromUser: 7000, carbonIntensity: 0.4, sustainabilityRating: 8),
        "FR": CountryData(name: "France", continent: "Europe", distanceFromUser: 7500, carbonIntensity: 0.1, sustainabilityRating: 7),
        "IT": CountryData(name: "Italy", continent: "Europe", distanceFromUser: 8000, carbonIntensity: 0.3, sustainabilityRating: 6),
        "JP": CountryData(name: "Japan", continent: "Asia", distanceFromUser: 10000, carbonIntensity: 0.5, sustainabilityRating: 7),
        "AU": CountryData(name: "Australia", continent: "Oceania", distanceFromUser: 15000, carbonIntensity: 0.8, sustainabilityRating: 6)
    ]
    
    private init() {}
    
    // MARK: - Main Function: Get Sustainability Data
    func getSustainabilityData(for productName: String, origin: String?, category: String?) async -> SustainabilityInfo {
        print("🌱 Analyzing sustainability for: \(productName)")
        
        let carbonFootprint = calculateCarbonFootprint(productName: productName, category: category)
        let waterUsage = calculateWaterUsage(productName: productName, category: category)
        let landUse = calculateLandUse(productName: productName, category: category)
        let transportDistance = calculateTransportDistance(origin: origin)
        let packagingScore = analyzePackaging(productName: productName)
        let seasonalityScore = analyzeSeasonality(productName: productName)
        let localScore = calculateLocalScore(origin: origin)
        
        let overallScore = calculateOverallSustainabilityScore(
            carbon: carbonFootprint,
            transport: transportDistance,
            packaging: packagingScore,
            local: localScore,
            seasonality: seasonalityScore
        )
        
        let grade = calculateSustainabilityGrade(overallScore)
        let impacts = generateEnvironmentalImpacts(
            carbon: carbonFootprint,
            water: waterUsage,
            transport: transportDistance
        )
        let tips = generateSustainabilityTips(
            productName: productName,
            origin: origin,
            score: overallScore
        )
        let certifications = detectCertifications(productName: productName)
        let alternatives = suggestAlternatives(productName: productName, origin: origin)
        
        return SustainabilityInfo(
            carbonFootprint: carbonFootprint ?? 2.0,
            waterUsage: waterUsage ?? 1000,
            landUse: landUse ?? 2.0,
            transportDistance: transportDistance ?? 1000,
            packagingScore: packagingScore,
            seasonalityScore: seasonalityScore,
            localScore: Double(localScore),
            overallScore: Double(overallScore) / 10.0, // Convert to 1-10 scale
            grade: grade,
            environmentalImpacts: impacts,
            recommendations: tips,
            certifications: certifications,
            alternatives: alternatives
        )
    }
    
    // MARK: - Carbon Footprint Calculation
    private func calculateCarbonFootprint(productName: String, category: String?) -> Double? {
        let name = productName.lowercased()
        
        // Direct lookup
        for (key, value) in carbonFootprintDB {
            if name.contains(key) {
                return value
            }
        }
        
        // Category-based estimation
        if let category = category?.lowercased() {
            switch category {
            case let c where c.contains("meat"): return 15.0
            case let c where c.contains("dairy"): return 3.5
            case let c where c.contains("vegetable"): return 0.4
            case let c where c.contains("fruit"): return 0.3
            case let c where c.contains("grain"): return 1.0
            case let c where c.contains("snack"): return 3.0
            case let c where c.contains("beverage"): return 0.7
            default: return 2.0
            }
        }
        
        return 2.0 // Default estimate
    }
    
    // MARK: - Water Usage Calculation
    private func calculateWaterUsage(productName: String, category: String?) -> Double? {
        let name = productName.lowercased()
        
        for (key, value) in waterUsageDB {
            if name.contains(key) {
                return value
            }
        }
        
        // Category-based estimation
        if let category = category?.lowercased() {
            switch category {
            case let c where c.contains("meat"): return 8000
            case let c where c.contains("dairy"): return 2000
            case let c where c.contains("vegetable"): return 300
            case let c where c.contains("fruit"): return 900
            case let c where c.contains("grain"): return 1500
            default: return 1000
            }
        }
        
        return 1000 // Default estimate
    }
    
    // MARK: - Land Use Calculation
    private func calculateLandUse(productName: String, category: String?) -> Double? {
        let name = productName.lowercased()
        
        // Land use estimates (m² per kg)
        if name.contains("beef") { return 164 }
        if name.contains("lamb") { return 185 }
        if name.contains("cheese") { return 41 }
        if name.contains("chicken") { return 7.5 }
        if name.contains("pork") { return 11 }
        if name.contains("fish") { return 3.7 }
        if name.contains("eggs") { return 5.7 }
        if name.contains("milk") { return 8.9 }
        if name.contains("vegetables") { return 0.3 }
        if name.contains("fruits") { return 0.8 }
        if name.contains("grains") { return 1.8 }
        
        return 2.0 // Default estimate
    }
    
    // MARK: - Transport Distance Calculation
    private func calculateTransportDistance(origin: String?) -> Double? {
        guard let origin = origin else { return nil }
        
        // Extract country code from origin
        let countryCode = extractCountryCode(from: origin)
        
        if let countryData = countryDatabase[countryCode] {
            return countryData.distanceFromUser
        }
        
        // Estimate based on origin string
        if origin.lowercased().contains("local") || origin.lowercased().contains("canada") {
            return 100
        } else if origin.lowercased().contains("usa") || origin.lowercased().contains("united states") {
            return 500
        } else if origin.lowercased().contains("mexico") {
            return 2000
        } else if origin.lowercased().contains("china") {
            return 11000
        } else if origin.lowercased().contains("europe") {
            return 7000
        }
        
        return 5000 // Default international distance
    }
    
    // MARK: - Packaging Analysis
    private func analyzePackaging(productName: String) -> Int {
        let name = productName.lowercased()
        
        // Packaging sustainability score (1-10, 10 = best)
        if name.contains("bulk") || name.contains("loose") { return 9 }
        if name.contains("glass") { return 8 }
        if name.contains("paper") || name.contains("cardboard") { return 7 }
        if name.contains("aluminum") || name.contains("can") { return 6 }
        if name.contains("plastic") { return 3 }
        if name.contains("styrofoam") { return 1 }
        
        // Default based on product type
        if name.contains("fresh") || name.contains("produce") { return 8 }
        if name.contains("packaged") || name.contains("processed") { return 4 }
        
        return 5 // Default score
    }
    
    // MARK: - Seasonality Analysis
    private func analyzeSeasonality(productName: String) -> Int {
        let name = productName.lowercased()
        let currentMonth = Calendar.current.component(.month, from: Date())
        
        // Seasonal produce scoring (simplified for North America)
        let seasonalFoods: [String: [Int]] = [
            "apple": [9, 10, 11, 12, 1, 2], // Fall/Winter
            "strawberry": [5, 6, 7], // Late Spring/Early Summer
            "tomato": [6, 7, 8, 9], // Summer/Early Fall
            "pumpkin": [9, 10, 11], // Fall
            "citrus": [11, 12, 1, 2, 3], // Winter/Early Spring
            "berries": [6, 7, 8], // Summer
            "squash": [9, 10, 11], // Fall
            "leafy": [4, 5, 9, 10] // Spring/Fall
        ]
        
        for (food, months) in seasonalFoods {
            if name.contains(food) {
                return months.contains(currentMonth) ? 9 : 4
            }
        }
        
        // Non-seasonal items get neutral score
        return 6
    }
    
    // MARK: - Local Score Calculation
    private func calculateLocalScore(origin: String?) -> Int {
        guard let origin = origin else { return 3 }
        
        let originLower = origin.lowercased()
        
        if originLower.contains("local") || originLower.contains("canada") {
            return 10
        } else if originLower.contains("usa") || originLower.contains("north america") {
            return 7
        } else if originLower.contains("mexico") {
            return 5
        } else if originLower.contains("europe") {
            return 3
        } else if originLower.contains("asia") || originLower.contains("china") {
            return 2
        }
        
        return 4 // Default international score
    }
    
    // MARK: - Overall Sustainability Score
    private func calculateOverallSustainabilityScore(carbon: Double?, transport: Double?, packaging: Int, local: Int, seasonality: Int) -> Int {
        var score = 50 // Base score
        
        // Carbon footprint impact (negative)
        if let carbon = carbon {
            if carbon < 1.0 { score += 20 }
            else if carbon < 3.0 { score += 10 }
            else if carbon > 10.0 { score -= 20 }
            else if carbon > 5.0 { score -= 10 }
        }
        
        // Transport distance impact (negative)
        if let transport = transport {
            if transport < 500 { score += 15 }
            else if transport < 2000 { score += 5 }
            else if transport > 8000 { score -= 15 }
            else if transport > 5000 { score -= 10 }
        }
        
        // Positive factors
        score += (packaging - 5) * 2 // Packaging score adjustment
        score += (local - 5) * 3 // Local score has high impact
        score += (seasonality - 5) * 2 // Seasonality adjustment
        
        return max(0, min(100, score))
    }
    
    // MARK: - Helper Functions
    private func calculateSustainabilityGrade(_ score: Int) -> String {
        switch score {
        case 80...100: return "A"
        case 60...79: return "B"
        case 40...59: return "C"
        case 20...39: return "D"
        default: return "E"
        }
    }
    
    private func extractCountryCode(from origin: String) -> String {
        // Simple country code extraction
        if origin.contains("Canada") { return "CA" }
        if origin.contains("United States") || origin.contains("USA") { return "US" }
        if origin.contains("Mexico") { return "MX" }
        if origin.contains("China") { return "CN" }
        if origin.contains("Germany") { return "DE" }
        if origin.contains("France") { return "FR" }
        if origin.contains("Italy") { return "IT" }
        if origin.contains("Japan") { return "JP" }
        if origin.contains("Australia") { return "AU" }
        if origin.contains("Brazil") { return "BR" }
        if origin.contains("India") { return "IN" }
        
        return "US" // Default
    }
    
    private func generateEnvironmentalImpacts(carbon: Double?, water: Double?, transport: Double?) -> [String] {
        var impacts: [String] = []
        
        if let carbon = carbon, carbon > 5.0 {
            impacts.append("High carbon footprint (\(String(format: "%.1f", carbon)) kg CO₂)")
        }
        
        if let water = water, water > 5000 {
            impacts.append("High water usage (\(Int(water)) liters)")
        }
        
        if let transport = transport, transport > 5000 {
            impacts.append("Long transport distance (\(Int(transport)) km)")
        }
        
        return impacts
    }
    
    private func generateSustainabilityTips(productName: String, origin: String?, score: Int) -> [String] {
        var tips: [String] = []
        
        if score < 50 {
            tips.append("Look for local alternatives to reduce environmental impact")
        }
        
        if let origin = origin, !origin.lowercased().contains("local") && !origin.lowercased().contains("canada") {
            tips.append("Choose locally produced items when possible")
        }
        
        if productName.lowercased().contains("plastic") {
            tips.append("Consider products with less packaging")
        }
        
        tips.append("Buy only what you need to reduce food waste")
        
        return tips
    }
    
    private func detectCertifications(productName: String) -> [String] {
        var certifications: [String] = []
        let name = productName.lowercased()
        
        if name.contains("organic") { certifications.append("Organic") }
        if name.contains("fair trade") { certifications.append("Fair Trade") }
        if name.contains("rainforest") { certifications.append("Rainforest Alliance") }
        if name.contains("non-gmo") { certifications.append("Non-GMO") }
        if name.contains("sustainable") { certifications.append("Sustainable") }
        
        return certifications
    }
    
    private func suggestAlternatives(productName: String, origin: String?) -> [String] {
        var alternatives: [String] = []
        
        if let origin = origin, !origin.lowercased().contains("local") {
            alternatives.append("Look for locally grown versions")
        }
        
        alternatives.append("Consider seasonal alternatives")
        alternatives.append("Check for organic options")
        alternatives.append("Look for minimal packaging versions")
        
        return alternatives
    }
}
