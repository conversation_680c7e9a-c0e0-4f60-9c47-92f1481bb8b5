//
//  ModernSettingsView.swift
//  IsitLocal?
//
//  Created by Augment Agent on 2025-06-11.
//  Modern beautiful settings view with enhanced UI
//

import SwiftUI
import CoreData

struct ModernSettingsView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @State private var showingCacheAlert = false
    @State private var showingAIConfig = false
    @State private var showingSecuritySettings = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.4, green: 0.49, blue: 0.92),  // #6b7eed
                        Color(red: 0.46, green: 0.29, blue: 0.64)  // #764ba2
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 30) {
                        // Header
                        VStack(spacing: 16) {
                            Image(systemName: "gearshape.2.fill")
                                .font(.system(size: 48, weight: .light))
                                .foregroundColor(.white)
                                .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 2)
                            
                            Text("Settings")
                                .font(.system(size: 28, weight: .bold))
                                .foregroundColor(.white)
                            
                            Text("Customize your IsitLocal experience")
                                .font(.system(size: 16, weight: .light))
                                .foregroundColor(.white.opacity(0.9))
                        }
                        .padding(.top, 20)
                        
                        // Settings Sections
                        VStack(spacing: 20) {
                            // AI Configuration Section
                            SettingsSection(
                                title: "AI Features",
                                items: [
                                    SettingsItem(
                                        icon: "brain.head.profile",
                                        iconColor: .orange,
                                        title: "IsitLocal AI Configuration",
                                        subtitle: "Configure AI-powered features",
                                        action: { showingAIConfig = true }
                                    )
                                ]
                            )
                            
                            // Security Section
                            SettingsSection(
                                title: "Security & Privacy",
                                items: [
                                    SettingsItem(
                                        icon: "lock.shield.fill",
                                        iconColor: .blue,
                                        title: "Security Settings",
                                        subtitle: "Biometric authentication & data protection",
                                        action: { showingSecuritySettings = true }
                                    )
                                ]
                            )
                            
                            // Data Management Section
                            SettingsSection(
                                title: "Data Management",
                                items: [
                                    SettingsItem(
                                        icon: "arrow.down.circle.fill",
                                        iconColor: .green,
                                        title: "Offline Products",
                                        subtitle: "Manage downloaded product data",
                                        action: { /* Navigate to offline view */ }
                                    ),
                                    SettingsItem(
                                        icon: "trash.fill",
                                        iconColor: .red,
                                        title: "Clear Cache",
                                        subtitle: "Remove cached product information",
                                        action: { showingCacheAlert = true }
                                    )
                                ]
                            )
                            
                            // App Information Section
                            SettingsSection(
                                title: "About",
                                items: [
                                    SettingsItem(
                                        icon: "info.circle.fill",
                                        iconColor: .purple,
                                        title: "App Information",
                                        subtitle: "Version, support, and legal",
                                        action: { /* Navigate to app info */ }
                                    ),
                                    SettingsItem(
                                        icon: "star.fill",
                                        iconColor: .yellow,
                                        title: "Rate IsitLocal",
                                        subtitle: "Help us improve with your feedback",
                                        action: { /* Open app store rating */ }
                                    )
                                ]
                            )
                        }
                        .padding(.horizontal, 20)
                        
                        Spacer(minLength: 120)
                    }
                }
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingAIConfig) {
            IsitLocalAIConfigurationView()
        }
        .sheet(isPresented: $showingSecuritySettings) {
            SecuritySettingsView()
        }
        .alert(isPresented: $showingCacheAlert) {
            Alert(
                title: Text("Clear Cache"),
                message: Text("This will clear all cached product data. This action cannot be undone."),
                primaryButton: .destructive(Text("Clear")) {
                    clearCache()
                },
                secondaryButton: .cancel()
            )
        }
    }
    
    private func clearCache() {
        PerformanceOptimizer.shared.cleanupCache(context: viewContext)
    }
}

// MARK: - Settings Section
struct SettingsSection: View {
    let title: String
    let items: [SettingsItem]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(title)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
                .padding(.horizontal, 20)
            
            VStack(spacing: 12) {
                ForEach(items.indices, id: \.self) { index in
                    SettingsItemView(item: items[index])
                }
            }
        }
    }
}

// MARK: - Settings Item Model
struct SettingsItem {
    let icon: String
    let iconColor: Color
    let title: String
    let subtitle: String
    let action: () -> Void
}

// MARK: - Settings Item View
struct SettingsItemView: View {
    let item: SettingsItem
    
    var body: some View {
        Button(action: item.action) {
            HStack(spacing: 16) {
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                colors: [item.iconColor, item.iconColor.opacity(0.8)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 44, height: 44)
                    
                    Image(systemName: item.icon)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(item.title)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.primary)
                    
                    Text(item.subtitle)
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.secondary)
            }
            .padding(16)
            .background(.ultraThinMaterial)
            .clipShape(RoundedRectangle(cornerRadius: 16))
            .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
        .padding(.horizontal, 20)
    }
}

#Preview {
    ModernSettingsView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
