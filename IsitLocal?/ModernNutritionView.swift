import SwiftUI

struct ModernNutritionView: View {
    let productName: String
    let barcode: String
    let nutritionInfo: NutritionInfo?
    
    @Environment(\.presentationMode) private var presentationMode
    @State private var isLoading = false
    @State private var currentNutritionInfo: NutritionInfo?
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.4, green: 0.49, blue: 0.92),  // #667eea
                        Color(red: 0.46, green: 0.29, blue: 0.64)  // #764ba2
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 24) {
                        // Header
                        VStack(spacing: 12) {
                            Image(systemName: "heart.fill")
                                .font(.system(size: 48, weight: .light))
                                .foregroundColor(.pink)
                            
                            Text("Nutrition Information")
                                .font(.system(size: 28, weight: .bold))
                                .foregroundColor(.primary)
                            
                            Text(productName)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                        }
                        .padding(.top, 20)
                        
                        if isLoading {
                            LoadingCard()
                        } else if let nutrition = currentNutritionInfo {
                            // Nutrition Content
                            VStack(spacing: 20) {
                                // Health Score Card
                                HealthScoreCard(nutrition: nutrition)
                                
                                // Macronutrients Card
                                MacronutrientsCard(nutrition: nutrition)
                                
                                // Detailed Nutrition Card
                                DetailedNutritionCard(nutrition: nutrition)
                                
                                // Health Insights Card
                                HealthInsightsCard(nutrition: nutrition)
                                
                                // Allergens Card
                                if !nutrition.allergens.isEmpty {
                                    AllergensCard(allergens: nutrition.allergens)
                                }
                            }
                        } else {
                            NoDataCard()
                        }
                        
                        Spacer(minLength: 100)
                    }
                    .padding(.horizontal, 20)
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(.primary)
                }
                
                ToolbarItem(placement: .principal) {
                    Text("Nutrition")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.primary)
                }
            }
        }
        .onAppear {
            loadNutritionData()
        }
    }
    
    private func loadNutritionData() {
        if let existingInfo = nutritionInfo {
            currentNutritionInfo = existingInfo
        } else {
            isLoading = true
            Task {
                let nutrition = await NutritionDataManager.shared.getNutritionData(for: productName, barcode: barcode)
                
                DispatchQueue.main.async {
                    self.currentNutritionInfo = nutrition
                    self.isLoading = false
                }
            }
        }
    }
}

// MARK: - Supporting Views

struct HealthScoreCard: View {
    let nutrition: NutritionInfo
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "chart.bar.fill")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.green)
                
                Text("Health Score")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            HStack(spacing: 20) {
                // Score Circle
                ZStack {
                    Circle()
                        .stroke(Color.gray.opacity(0.3), lineWidth: 8)
                        .frame(width: 80, height: 80)
                    
                    Circle()
                        .trim(from: 0, to: CGFloat(nutrition.healthScore) / 10.0)
                        .stroke(
                            LinearGradient(
                                colors: [.green, .yellow, .red],
                                startPoint: .leading,
                                endPoint: .trailing
                            ),
                            style: StrokeStyle(lineWidth: 8, lineCap: .round)
                        )
                        .frame(width: 80, height: 80)
                        .rotationEffect(.degrees(-90))
                    
                    Text("\(Int(nutrition.healthScore * 10))")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.primary)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(nutrition.healthGrade)
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(gradeColor(nutrition.healthGrade))
                    
                    Text("Health Grade")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
        }
        .padding(20)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
    
    private func gradeColor(_ grade: String) -> Color {
        switch grade.uppercased() {
        case "A": return .green
        case "B": return .blue
        case "C": return .orange
        case "D", "E": return .red
        default: return .gray
        }
    }
}

struct MacronutrientsCard: View {
    let nutrition: NutritionInfo
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "chart.pie.fill")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.blue)
                
                Text("Macronutrients")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            VStack(spacing: 12) {
                MacroRow(label: "Calories", value: "\(Int(nutrition.calories))", unit: "kcal", color: .orange)
                MacroRow(label: "Protein", value: String(format: "%.1f", nutrition.protein), unit: "g", color: .red)
                MacroRow(label: "Carbs", value: String(format: "%.1f", nutrition.carbohydrates), unit: "g", color: .blue)
                MacroRow(label: "Fat", value: String(format: "%.1f", nutrition.fat), unit: "g", color: .purple)
                MacroRow(label: "Fiber", value: String(format: "%.1f", nutrition.fiber), unit: "g", color: .green)
            }
        }
        .padding(20)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

struct MacroRow: View {
    let label: String
    let value: String
    let unit: String
    let color: Color
    
    var body: some View {
        HStack {
            Circle()
                .fill(color)
                .frame(width: 12, height: 12)
            
            Text(label)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.primary)
            
            Spacer()
            
            Text("\(value) \(unit)")
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.primary)
        }
    }
}

struct DetailedNutritionCard: View {
    let nutrition: NutritionInfo
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "list.bullet")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.indigo)
                
                Text("Detailed Nutrition")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            VStack(spacing: 8) {
                NutritionRow(label: "Sodium", value: String(format: "%.0f", nutrition.sodium), unit: "mg")
                NutritionRow(label: "Sugar", value: String(format: "%.1f", nutrition.sugar), unit: "g")
                NutritionRow(label: "Saturated Fat", value: String(format: "%.1f", nutrition.saturatedFat), unit: "g")
                NutritionRow(label: "Cholesterol", value: String(format: "%.0f", nutrition.cholesterol), unit: "mg")
            }
        }
        .padding(20)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

struct NutritionRow: View {
    let label: String
    let value: String
    let unit: String
    
    var body: some View {
        HStack {
            Text(label)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text("\(value) \(unit)")
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.primary)
        }
        .padding(.vertical, 4)
    }
}

struct HealthInsightsCard: View {
    let nutrition: NutritionInfo
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "lightbulb.fill")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.yellow)
                
                Text("Health Insights")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 12) {
                if !nutrition.warnings.isEmpty {
                    ForEach(nutrition.warnings, id: \.self) { warning in
                        InsightRow(text: warning, type: .warning)
                    }
                }
                
                if !nutrition.benefits.isEmpty {
                    ForEach(nutrition.benefits, id: \.self) { benefit in
                        InsightRow(text: benefit, type: .benefit)
                    }
                }
            }
        }
        .padding(20)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

struct InsightRow: View {
    let text: String
    let type: InsightType
    
    enum InsightType {
        case warning, benefit
        
        var icon: String {
            switch self {
            case .warning: return "exclamationmark.triangle.fill"
            case .benefit: return "checkmark.circle.fill"
            }
        }
        
        var color: Color {
            switch self {
            case .warning: return .orange
            case .benefit: return .green
            }
        }
    }
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: type.icon)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(type.color)
                .padding(.top, 2)
            
            Text(text)
                .font(.system(size: 14))
                .foregroundColor(.primary)
                .multilineTextAlignment(.leading)
            
            Spacer()
        }
    }
}

struct AllergensCard: View {
    let allergens: [String]
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "exclamationmark.shield.fill")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.red)
                
                Text("Allergen Information")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 8) {
                ForEach(allergens, id: \.self) { allergen in
                    Text(allergen)
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundColor(.red)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.red.opacity(0.1))
                        .clipShape(Capsule())
                }
            }
        }
        .padding(20)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

struct LoadingCard: View {
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Loading nutrition information...")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.secondary)
        }
        .frame(height: 200)
        .frame(maxWidth: .infinity)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

struct NoDataCard: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.circle")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(.gray)
            
            Text("No nutrition data available")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.primary)
            
            Text("Nutrition information could not be found for this product.")
                .font(.system(size: 14))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(height: 200)
        .frame(maxWidth: .infinity)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

#Preview {
    ModernNutritionView(
        productName: "Organic Maple Syrup",
        barcode: "123456789",
        nutritionInfo: nil
    )
}
