//
//  IsitLocalAIEnhancedView.swift
//  IsitLocal?
//
//  Created by Augment Agent on 2025-06-11.
//  AI Enhanced product information view (powered by Gemini but branded as IsitLocal AI)
//

import SwiftUI

struct IsitLocalAIEnhancedView: View {
    let barcode: String
    let productName: String?
    let manufacturer: String?
    let origin: String?
    
    @State private var enhancedDescription: String = ""
    @State private var nutritionHighlights: String = ""
    @State private var sustainabilityInfo: String = ""
    @State private var localAlternatives: [LocalAlternative] = []
    @State private var healthConsiderations: String = ""
    @State private var dataValidation: AIValidationResult = AIValidationResult()
    @State private var isLoading: Bool = false
    @State private var errorMessage: String = ""
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 1.0, green: 0.42, blue: 0.42),  // #ff6b6b
                        Color(red: 0.93, green: 0.35, blue: 0.14)  // #ee5a24
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 30) {
                        // Header
                        VStack(spacing: 16) {
                            Image(systemName: "brain.head.profile")
                                .font(.system(size: 48, weight: .light))
                                .foregroundColor(.white)
                                .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 2)
                            
                            Text("IsitLocal AI Enhanced Info")
                                .font(.system(size: 24, weight: .bold))
                                .foregroundColor(.white)
                            
                            Text("Powered by advanced AI analysis")
                                .font(.system(size: 16, weight: .light))
                                .foregroundColor(.white.opacity(0.9))
                        }
                        .padding(.top, 20)
                        
                        // Product Header
                        VStack(spacing: 12) {
                            Text(productName ?? "Unknown Product")
                                .font(.system(size: 22, weight: .bold))
                                .foregroundColor(.white)
                                .multilineTextAlignment(.center)
                            
                            if let manufacturer = manufacturer {
                                Text("by \(manufacturer)")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.white.opacity(0.9))
                            }
                            
                            if !dataValidation.isValid.isEmpty {
                                HStack(spacing: 8) {
                                    Image(systemName: "checkmark.seal.fill")
                                        .font(.system(size: 16))
                                        .foregroundColor(.green)
                                    
                                    Text("AI Confidence: \(dataValidation.confidenceScore)/10")
                                        .font(.system(size: 12, weight: .semibold))
                                        .foregroundColor(.white)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 4)
                                        .background(Color.green.opacity(0.8))
                                        .clipShape(Capsule())
                                }
                            }
                        }
                        .padding(20)
                        .background(.ultraThinMaterial)
                        .clipShape(RoundedRectangle(cornerRadius: 16))
                        .padding(.horizontal, 20)
                        
                        if isLoading {
                            LoadingCard()
                        } else if !errorMessage.isEmpty {
                            ErrorCard(message: errorMessage, onRetry: loadAIInsights)
                        } else {
                            // AI Insights Cards
                            VStack(spacing: 20) {
                                if !enhancedDescription.isEmpty {
                                    InsightCard(
                                        icon: "text.bubble.fill",
                                        iconColor: Color(red: 0.4, green: 0.49, blue: 0.92),
                                        title: "Enhanced Description",
                                        content: enhancedDescription
                                    )
                                }
                                
                                if !nutritionHighlights.isEmpty {
                                    InsightCard(
                                        icon: "heart.fill",
                                        iconColor: .pink,
                                        title: "Nutrition Highlights",
                                        content: nutritionHighlights
                                    )
                                }
                                
                                if !sustainabilityInfo.isEmpty {
                                    InsightCard(
                                        icon: "leaf.fill",
                                        iconColor: .green,
                                        title: "Sustainability Impact",
                                        content: sustainabilityInfo
                                    )
                                }
                                
                                if !dataValidation.isValid.isEmpty {
                                    ValidationCard(validation: dataValidation)
                                }
                                
                                if !localAlternatives.isEmpty {
                                    LocalAlternativesCard(alternatives: localAlternatives)
                                }
                                
                                if !healthConsiderations.isEmpty {
                                    InsightCard(
                                        icon: "cross.case.fill",
                                        iconColor: .blue,
                                        title: "Health Considerations",
                                        content: healthConsiderations
                                    )
                                }
                            }
                            .padding(.horizontal, 20)
                        }
                        
                        // Refresh Button
                        Button(action: loadAIInsights) {
                            HStack(spacing: 8) {
                                Image(systemName: "arrow.clockwise")
                                    .font(.system(size: 16, weight: .semibold))
                                Text("Refresh AI Analysis")
                                    .font(.system(size: 16, weight: .semibold))
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 50)
                            .background(
                                LinearGradient(
                                    colors: [Color(red: 0.4, green: 0.49, blue: 0.92), Color(red: 0.46, green: 0.29, blue: 0.64)],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                            .shadow(color: Color(red: 0.4, green: 0.49, blue: 0.92).opacity(0.4), radius: 8, x: 0, y: 4)
                        }
                        .disabled(isLoading)
                        .padding(.horizontal, 20)
                        
                        Spacer(minLength: 40)
                    }
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(.white)
                }
            }
            .onAppear {
                loadAIInsights()
            }
        }
    }
    
    private func loadAIInsights() {
        isLoading = true
        errorMessage = ""
        
        // Use GeminiAPIManager but present as IsitLocal AI
        Task {
            do {
                let insights = try await GeminiAPIManager.shared.getEnhancedProductInfo(
                    productName: productName ?? "Unknown Product",
                    manufacturer: manufacturer,
                    barcode: barcode,
                    origin: origin
                )
                
                await MainActor.run {
                    self.enhancedDescription = insights.enhancedDescription
                    self.nutritionHighlights = insights.nutritionHighlights
                    self.sustainabilityInfo = insights.sustainabilityInfo
                    self.localAlternatives = insights.localAlternatives.map { alt in
                        LocalAlternative(
                            name: alt.name,
                            description: alt.description,
                            location: alt.location
                        )
                    }
                    self.healthConsiderations = insights.healthConsiderations
                    self.dataValidation = AIValidationResult(
                        isValid: insights.dataValidation.isValid,
                        confidenceScore: insights.dataValidation.confidenceScore,
                        issues: insights.dataValidation.issues
                    )
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "Unable to load AI insights. Please check your IsitLocal AI configuration."
                    self.isLoading = false
                }
            }
        }
    }
}

// MARK: - Supporting Data Structures
struct LocalAlternative {
    let name: String
    let description: String
    let location: String
}

struct AIValidationResult {
    let isValid: String
    let confidenceScore: Int
    let issues: [String]

    init() {
        self.isValid = ""
        self.confidenceScore = 0
        self.issues = []
    }

    init(isValid: String, confidenceScore: Int, issues: [String]) {
        self.isValid = isValid
        self.confidenceScore = confidenceScore
        self.issues = issues
    }
}

// MARK: - Supporting Views
struct LoadingCard: View {
    @State private var rotationAngle: Double = 0
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "brain.head.profile")
                .font(.system(size: 40))
                .foregroundColor(Color(red: 0.4, green: 0.49, blue: 0.92))
                .rotationEffect(.degrees(rotationAngle))
                .animation(.linear(duration: 2).repeatForever(autoreverses: false), value: rotationAngle)
                .onAppear {
                    rotationAngle = 360
                }
            
            Text("IsitLocal AI is analyzing...")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
        }
        .frame(maxWidth: .infinity)
        .padding(40)
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal, 20)
    }
}

struct ErrorCard: View {
    let message: String
    let onRetry: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 32))
                .foregroundColor(.orange)
            
            Text(message)
                .font(.system(size: 16))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
            
            Button("Try Again", action: onRetry)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 8)
                .background(Color.orange)
                .clipShape(Capsule())
        }
        .frame(maxWidth: .infinity)
        .padding(30)
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal, 20)
    }
}

struct InsightCard: View {
    let icon: String
    let iconColor: Color
    let title: String
    let content: String

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack(spacing: 12) {
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                colors: [iconColor, iconColor.opacity(0.8)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 40, height: 40)

                    Image(systemName: icon)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)
                }

                Text(title)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)

                Spacer()
            }

            Text(content)
                .font(.system(size: 15))
                .foregroundColor(.secondary)
                .lineLimit(nil)
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

struct ValidationCard: View {
    let validation: AIValidationResult

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack(spacing: 12) {
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                colors: [Color.green, Color.teal],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 40, height: 40)

                    Image(systemName: "checkmark.shield.fill")
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)
                }

                Text("Data Validation")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)

                Spacer()

                Text("Confidence: \(validation.confidenceScore)/10")
                    .font(.system(size: 12, weight: .semibold))
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.green)
                    .clipShape(Capsule())
            }

            HStack(spacing: 8) {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 20))
                    .foregroundColor(.green)

                Text("Data appears valid")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.green)
            }

            Text(validation.isValid)
                .font(.system(size: 15))
                .foregroundColor(.secondary)
                .lineLimit(nil)
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.green.opacity(0.3), lineWidth: 2)
        )
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

struct LocalAlternativesCard: View {
    let alternatives: [LocalAlternative]

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack(spacing: 12) {
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                colors: [Color.orange, Color.red],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 40, height: 40)

                    Image(systemName: "location.fill")
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)
                }

                Text("Local Alternatives")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)

                Spacer()
            }

            VStack(spacing: 12) {
                ForEach(alternatives.indices, id: \.self) { index in
                    let alternative = alternatives[index]

                    VStack(alignment: .leading, spacing: 8) {
                        Text(alternative.name)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.primary)

                        Text(alternative.description)
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)

                        if !alternative.location.isEmpty {
                            HStack(spacing: 4) {
                                Image(systemName: "location")
                                    .font(.system(size: 12))
                                    .foregroundColor(.blue)

                                Text(alternative.location)
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                    .padding(12)
                    .background(Color(.systemGray6))
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.orange.opacity(0.3), lineWidth: 1, alignment: .leading)
                    )
                }
            }
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

#Preview {
    IsitLocalAIEnhancedView(
        barcode: "123456789012",
        productName: "Organic Maple Syrup",
        manufacturer: "Canadian Maple Co.",
        origin: "Canada"
    )
}
