//
//  SustainabilityView.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-02.
//

import SwiftUI
import CoreData

struct SustainabilityView: View {
    let barcode: String
    let origin: String
    let productName: String
    
    @Environment(\.managedObjectContext) private var viewContext
    @State private var sustainabilityInfo: SustainabilityInfo?
    @State private var isLoading = true
    
    private var sustainabilityManager: SustainabilityManager {
        return SustainabilityManager(context: viewContext)
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                        .scaleEffect(1.5)
                        .padding()
                } else if let info = sustainabilityInfo {
                    // Product info
                    VStack(alignment: .leading, spacing: 10) {
                        Text(productName)
                            .font(.title)
                            .fontWeight(.bold)
                        
                        Text("Origin: \(origin)")
                            .font(.headline)
                        
                        Text("Barcode: \(barcode)")
                            .font(.subheadline)
                            .foregroundColor(.gray)
                    }
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(Color.white.opacity(0.9))
                    .clipShape(RoundedRectangle(cornerRadius: 20))
                    .shadow(radius: 5)
                    .padding(.horizontal)
                    
                    // Eco score
                    VStack(spacing: 15) {
                        Text("Eco Score")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        ZStack {
                            Circle()
                                .stroke(lineWidth: 20)
                                .opacity(0.3)
                                .foregroundColor(Color.gray)
                            
                            Circle()
                                .trim(from: 0.0, to: CGFloat(info.ecoScore) / 100)
                                .stroke(style: StrokeStyle(lineWidth: 20, lineCap: .round, lineJoin: .round))
                                .foregroundColor(ecoScoreColor(score: info.ecoScore))
                                .rotationEffect(Angle(degrees: 270.0))
                                .animation(.linear, value: info.ecoScore)
                            
                            Text("\(info.ecoScore)/100")
                                .font(.title)
                                .fontWeight(.bold)
                        }
                        .frame(width: 150, height: 150)
                        
                        Text(ecoScoreDescription(score: info.ecoScore))
                            .font(.headline)
                            .foregroundColor(ecoScoreColor(score: info.ecoScore))
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }
                    .padding()
                    .background(Color.white.opacity(0.9))
                    .clipShape(RoundedRectangle(cornerRadius: 20))
                    .shadow(radius: 5)
                    .padding(.horizontal)
                    
                    // Environmental impact
                    VStack(alignment: .leading, spacing: 15) {
                        Text("Environmental Impact")
                            .font(.title2)
                            .fontWeight(.bold)
                            .padding(.bottom, 5)
                        
                        HStack {
                            Text("Transport Impact:")
                                .font(.headline)
                            Spacer()
                            Text("\(String(format: "%.1f", info.transportImpact)) CO2e")
                                .font(.headline)
                                .foregroundColor(.secondary)
                        }
                        
                        HStack {
                            Text("Carbon Footprint:")
                                .font(.headline)
                            Spacer()
                            Text("\(String(format: "%.1f", info.carbonFootprint)) kg CO2e")
                                .font(.headline)
                                .foregroundColor(.secondary)
                        }
                        
                        Divider()
                        
                        Text("Recommendations:")
                            .font(.headline)
                            .padding(.top, 5)
                        
                        Text("• Look for products with local origins to reduce transport emissions")
                            .font(.subheadline)
                            .padding(.vertical, 2)
                        
                        Text("• Consider seasonal products which typically have lower environmental impact")
                            .font(.subheadline)
                            .padding(.vertical, 2)
                        
                        Text("• Products with higher eco scores are generally more sustainable choices")
                            .font(.subheadline)
                            .padding(.vertical, 2)
                    }
                    .padding()
                    .background(Color.white.opacity(0.9))
                    .clipShape(RoundedRectangle(cornerRadius: 20))
                    .shadow(radius: 5)
                    .padding(.horizontal)
                } else {
                    Text("No sustainability information available")
                        .font(.headline)
                        .foregroundColor(.gray)
                        .padding()
                }
            }
            .padding(.vertical)
        }
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.4, green: 0.49, blue: 0.92),  // #667eea
                    Color(red: 0.46, green: 0.29, blue: 0.64)  // #764ba2
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
        )
        .navigationTitle("Sustainability")
        .onAppear {
            loadSustainabilityInfo()
        }
    }
    
    private func loadSustainabilityInfo() {
        isLoading = true
        
        // Check if we have cached info
        if let cachedInfo = sustainabilityManager.getSustainabilityInfo(barcode: barcode) {
            sustainabilityInfo = cachedInfo
            isLoading = false
        } else {
            // Calculate new sustainability info
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.sustainabilityInfo = self.sustainabilityManager.calculateSustainabilityScore(
                    barcode: self.barcode,
                    origin: self.origin,
                    productType: "Food" // Default to food category
                )
                self.isLoading = false
            }
        }
    }
    
    private func ecoScoreColor(score: Int16) -> Color {
        if score >= 80 {
            return .green
        } else if score >= 60 {
            return .yellow
        } else if score >= 40 {
            return .orange
        } else {
            return .red
        }
    }
    
    private func ecoScoreDescription(score: Int16) -> String {
        if score >= 80 {
            return "Excellent - Very low environmental impact"
        } else if score >= 60 {
            return "Good - Low environmental impact"
        } else if score >= 40 {
            return "Average - Moderate environmental impact"
        } else if score >= 20 {
            return "Poor - High environmental impact"
        } else {
            return "Very Poor - Very high environmental impact"
        }
    }
}
