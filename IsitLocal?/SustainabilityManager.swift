//
//  SustainabilityManager.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-02.
//

import Foundation
import CoreData

class SustainabilityManager {
    private let context: NSManagedObjectContext
    
    init(context: NSManagedObjectContext) {
        self.context = context
    }
    
    // Function to calculate sustainability score based on origin and product type
    func calculateSustainabilityScore(barcode: String, origin: String, productType: String) -> SustainabilityInfo {
        // This is a simplified version that returns sample data
        let info = SustainabilityInfo(context: context)
        info.barcode = barcode
        info.origin = origin
        info.productType = productType
        
        // Calculate transport impact based on origin
        var transportImpact: Double = 0
        if origin.contains("United States") {
            transportImpact = 15.0
        } else if origin.contains("China") {
            transportImpact = 75.0
        } else if origin.contains("Europe") {
            transportImpact = 45.0
        } else {
            transportImpact = 30.0
        }
        
        // Calculate eco score (0-100)
        let ecoScore = max(0, min(100, 100 - Int16(transportImpact / 100 * 50)))
        
        info.transportImpact = transportImpact
        info.carbonFootprint = transportImpact * 1.5
        info.ecoScore = ecoScore
        info.calculationDate = Date()
        
        do {
            try context.save()
            return info
        } catch {
            print("Failed to save sustainability info: \(error)")
            return info
        }
    }
    
    // Function to get sustainability info from cache
    func getSustainabilityInfo(barcode: String) -> SustainabilityInfo? {
        let fetchRequest: NSFetchRequest<SustainabilityInfo> = SustainabilityInfo.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "barcode == %@", barcode)
        fetchRequest.fetchLimit = 1
        
        do {
            let results = try context.fetch(fetchRequest)
            return results.first
        } catch {
            print("Failed to fetch sustainability info: \(error)")
            return nil
        }
    }
}
