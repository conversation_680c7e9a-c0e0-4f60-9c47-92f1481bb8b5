//
//  StatisticsView.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-02.
//

import SwiftUI
import CoreData

struct StatisticsView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Item.timestamp, ascending: false)],
        animation: .default)
    private var items: FetchedResults<Item>
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    Text("Your Scanning Statistics")
                        .font(.title)
                        .fontWeight(.bold)
                        .padding(.top)
                    
                    // Total scans card
                    StatCard(title: "Total Scans", value: "\(items.count)", icon: "barcode", color: .blue)
                    
                    // Origin distribution
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Product Origins")
                            .font(.headline)
                            .padding(.horizontal)
                        
                        if items.isEmpty {
                            Text("No scan data available yet")
                                .font(.subheadline)
                                .foregroundColor(.gray)
                                .padding()
                                .frame(maxWidth: .infinity, alignment: .center)
                        } else {
                            let originStats = calculateOriginStats()
                            
                            ForEach(originStats.sorted(by: { $0.count > $1.count }), id: \.origin) { stat in
                                OriginBar(origin: stat.origin, count: stat.count, total: items.count)
                            }
                        }
                    }
                    .padding(.vertical)
                    .background(Color.white)
                    .cornerRadius(15)
                    .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                    .padding(.horizontal)
                    
                    // Recent activity
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Recent Activity")
                            .font(.headline)
                            .padding(.horizontal)
                        
                        if items.isEmpty {
                            Text("No recent scans")
                                .font(.subheadline)
                                .foregroundColor(.gray)
                                .padding()
                                .frame(maxWidth: .infinity, alignment: .center)
                        } else {
                            ForEach(Array(items.prefix(5)), id: \.self) { item in
                                HStack {
                                    VStack(alignment: .leading) {
                                        Text(item.productName ?? "Unknown Product")
                                            .font(.subheadline)
                                            .fontWeight(.semibold)
                                        
                                        Text(item.origin ?? "Unknown Origin")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                    
                                    Spacer()
                                    
                                    if let timestamp = item.timestamp {
                                        Text(timestamp, formatter: itemFormatter)
                                            .font(.caption)
                                            .foregroundColor(.gray)
                                    }
                                }
                                .padding(.vertical, 8)
                                .padding(.horizontal)
                                
                                if item != items.prefix(5).last {
                                    Divider()
                                        .padding(.horizontal)
                                }
                            }
                        }
                    }
                    .padding(.vertical)
                    .background(Color.white)
                    .cornerRadius(15)
                    .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                    .padding(.horizontal)
                    
                    Spacer()
                }
                .padding(.bottom)
            }
            .background(Color.gray.opacity(0.1).ignoresSafeArea())
            .navigationTitle("Statistics")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    private func calculateOriginStats() -> [OriginStat] {
        var stats: [String: Int] = [:]
        
        for item in items {
            if let origin = item.origin {
                // Extract country name without emoji
                let countryName = origin.components(separatedBy: " ").first ?? origin
                stats[countryName, default: 0] += 1
            } else {
                stats["Unknown", default: 0] += 1
            }
        }
        
        return stats.map { OriginStat(origin: $0.key, count: $0.value) }
    }
}

struct OriginStat {
    let origin: String
    let count: Int
}

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 15) {
            Image(systemName: icon)
                .font(.largeTitle)
                .foregroundColor(color)
                .frame(width: 60, height: 60)
                .background(color.opacity(0.2))
                .clipShape(Circle())
            
            VStack(alignment: .leading, spacing: 5) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.secondary)
                
                Text(value)
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
            }
            
            Spacer()
        }
        .padding()
        .background(Color.white)
        .cornerRadius(15)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        .padding(.horizontal)
    }
}

struct OriginBar: View {
    let origin: String
    let count: Int
    let total: Int
    
    var percentage: Double {
        return Double(count) / Double(total)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 5) {
            HStack {
                Text(origin)
                    .font(.subheadline)
                
                Spacer()
                
                Text("\(count) (\(Int(percentage * 100))%)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .frame(width: geometry.size.width, height: 10)
                        .opacity(0.1)
                        .foregroundColor(.gray)
                    
                    Rectangle()
                        .frame(width: geometry.size.width * CGFloat(percentage), height: 10)
                        .foregroundColor(.blue)
                }
                .cornerRadius(5)
            }
            .frame(height: 10)
        }
        .padding(.horizontal)
        .padding(.vertical, 5)
    }
}

private let itemFormatter: DateFormatter = {
    let formatter = DateFormatter()
    formatter.dateStyle = .short
    formatter.timeStyle = .short
    return formatter
}()
