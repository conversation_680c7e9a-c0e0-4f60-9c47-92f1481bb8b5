//
//  NutritionManager.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-02.
//

import Foundation
import CoreData

class NutritionManager {
    // Function to get nutrition info (from cache or API)
    func getNutritionInfo(code: String, context: NSManagedObjectContext) async -> NutritionInfo? {
        // This is a simplified version that returns sample data
        print("Nutrition - Getting info for barcode \(code)")
        
        // Create a new NutritionInfo object with sample data
        let nutritionInfo = NutritionInfo(context: context)
        nutritionInfo.barcode = code
        nutritionInfo.calories = "240"
        nutritionInfo.fat = "12g"
        nutritionInfo.carbohydrates = "30g"
        nutritionInfo.protein = "5g"
        nutritionInfo.sugar = "15g"
        nutritionInfo.fiber = "3g"
        nutritionInfo.sodium = "120mg"
        nutritionInfo.servingSize = "100g"
        nutritionInfo.lastUpdated = Date()
        
        do {
            try context.save()
            return nutritionInfo
        } catch {
            print("Nutrition - Error saving to context: \(error)")
            return nil
        }
    }
}
