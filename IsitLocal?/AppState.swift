//
//  AppState.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-02.
//

import Foundation
import SwiftUI

// Class to manage global app state
class AppState: ObservableObject {
    // Flag to track if the app has fully initialized
    @Published var isInitialized = false

    // Flag to track if the app is in debug mode
    @Published var isDebugMode = false

    // Flag to track if the app is currently loading data
    @Published var isLoading = false

    // Tab navigation
    @Published var shouldNavigateToFavorites = false

    // Initialize with provided values
    init(isDebugMode: Bool = false) {
        // Set debug mode based on parameter or build configuration
        self.isDebugMode = isDebugMode

        #if DEBUG
        if !isDebugMode {
            self.isDebugMode = true
        }
        #endif

        // Start with initialized = true for faster startup
        self.isInitialized = true
    }

    // Function to reset the app state
    func reset() {
        isLoading = false
    }
}
