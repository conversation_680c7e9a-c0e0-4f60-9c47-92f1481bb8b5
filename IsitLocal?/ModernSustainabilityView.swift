import SwiftUI

struct ModernSustainabilityView: View {
    let productName: String
    let origin: String
    let barcode: String
    let sustainabilityInfo: SustainabilityInfo?
    
    @Environment(\.presentationMode) private var presentationMode
    @State private var isLoading = false
    @State private var currentSustainabilityInfo: SustainabilityInfo?
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.95, green: 1.0, blue: 0.95),
                        Color(red: 0.98, green: 1.0, blue: 0.98)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 24) {
                        // Header
                        VStack(spacing: 12) {
                            Image(systemName: "leaf.fill")
                                .font(.system(size: 48, weight: .light))
                                .foregroundColor(.green)
                            
                            Text("Sustainability Score")
                                .font(.system(size: 28, weight: .bold))
                                .foregroundColor(.primary)
                            
                            Text(productName)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                        }
                        .padding(.top, 20)
                        
                        if isLoading {
                            LoadingSustainabilityCard()
                        } else if let sustainability = currentSustainabilityInfo {
                            // Sustainability Content
                            VStack(spacing: 20) {
                                // Overall Score Card
                                OverallScoreCard(sustainability: sustainability)
                                
                                // Environmental Impact Card
                                EnvironmentalImpactCard(sustainability: sustainability)
                                
                                // Local Score Card
                                LocalScoreCard(sustainability: sustainability, origin: origin)
                                
                                // Transport Impact Card
                                TransportImpactCard(sustainability: sustainability)
                                
                                // Recommendations Card
                                RecommendationsCard(sustainability: sustainability)
                            }
                        } else {
                            NoSustainabilityDataCard()
                        }
                        
                        Spacer(minLength: 100)
                    }
                    .padding(.horizontal, 20)
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(.primary)
                }
                
                ToolbarItem(placement: .principal) {
                    Text("Sustainability")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.primary)
                }
            }
        }
        .onAppear {
            loadSustainabilityData()
        }
    }
    
    private func loadSustainabilityData() {
        if let existingInfo = sustainabilityInfo {
            currentSustainabilityInfo = existingInfo
        } else {
            isLoading = true
            Task {
                let sustainability = await SustainabilityDataManager.shared.getSustainabilityData(
                    for: productName,
                    origin: origin,
                    category: nil
                )
                
                DispatchQueue.main.async {
                    self.currentSustainabilityInfo = sustainability
                    self.isLoading = false
                }
            }
        }
    }
}

// MARK: - Supporting Views

struct OverallScoreCard: View {
    let sustainability: SustainabilityInfo
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "chart.bar.fill")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.green)
                
                Text("Overall Sustainability")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            HStack(spacing: 20) {
                // Score Circle
                ZStack {
                    Circle()
                        .stroke(Color.gray.opacity(0.3), lineWidth: 8)
                        .frame(width: 80, height: 80)
                    
                    Circle()
                        .trim(from: 0, to: CGFloat(sustainability.overallScore) / 10.0)
                        .stroke(
                            LinearGradient(
                                colors: [.red, .orange, .yellow, .green],
                                startPoint: .leading,
                                endPoint: .trailing
                            ),
                            style: StrokeStyle(lineWidth: 8, lineCap: .round)
                        )
                        .frame(width: 80, height: 80)
                        .rotationEffect(.degrees(-90))
                    
                    Text(String(format: "%.1f", sustainability.overallScore))
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.primary)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(sustainability.grade)
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(gradeColor(sustainability.grade))
                    
                    Text("Sustainability Grade")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
        }
        .padding(20)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
    
    private func gradeColor(_ grade: String) -> Color {
        switch grade.uppercased() {
        case "A": return .green
        case "B": return .blue
        case "C": return .orange
        case "D", "E": return .red
        default: return .gray
        }
    }
}

struct EnvironmentalImpactCard: View {
    let sustainability: SustainabilityInfo
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "globe.americas.fill")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.blue)
                
                Text("Environmental Impact")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            VStack(spacing: 12) {
                ImpactRow(
                    icon: "cloud.fill",
                    label: "Carbon Footprint",
                    value: String(format: "%.2f", sustainability.carbonFootprint),
                    unit: "kg CO₂",
                    color: .orange
                )
                
                ImpactRow(
                    icon: "drop.fill",
                    label: "Water Usage",
                    value: String(format: "%.0f", sustainability.waterUsage),
                    unit: "liters",
                    color: .blue
                )
                
                ImpactRow(
                    icon: "leaf.fill",
                    label: "Land Use",
                    value: String(format: "%.1f", sustainability.landUse),
                    unit: "m²",
                    color: .green
                )
            }
        }
        .padding(20)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

struct ImpactRow: View {
    let icon: String
    let label: String
    let value: String
    let unit: String
    let color: Color
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(color)
                .frame(width: 20)
            
            Text(label)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.primary)
            
            Spacer()
            
            Text("\(value) \(unit)")
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.primary)
        }
    }
}

struct LocalScoreCard: View {
    let sustainability: SustainabilityInfo
    let origin: String
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "location.fill")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.purple)
                
                Text("Local Score")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            HStack(spacing: 20) {
                // Local Score Circle
                ZStack {
                    Circle()
                        .stroke(Color.gray.opacity(0.3), lineWidth: 6)
                        .frame(width: 60, height: 60)
                    
                    Circle()
                        .trim(from: 0, to: CGFloat(sustainability.localScore) / 10.0)
                        .stroke(Color.purple, style: StrokeStyle(lineWidth: 6, lineCap: .round))
                        .frame(width: 60, height: 60)
                        .rotationEffect(.degrees(-90))
                    
                    Text("\(Int(sustainability.localScore))")
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(.primary)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Origin: \(getOriginFlag(origin)) \(origin)")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.primary)
                    
                    Text("Distance: \(Int(sustainability.transportDistance)) km")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
        }
        .padding(20)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
    
    private func getOriginFlag(_ origin: String) -> String {
        let lowercased = origin.lowercased()
        if lowercased.contains("canada") { return "🇨🇦" }
        if lowercased.contains("usa") || lowercased.contains("united states") { return "🇺🇸" }
        if lowercased.contains("mexico") { return "🇲🇽" }
        if lowercased.contains("france") { return "🇫🇷" }
        if lowercased.contains("germany") { return "🇩🇪" }
        if lowercased.contains("italy") { return "🇮🇹" }
        if lowercased.contains("spain") { return "🇪🇸" }
        if lowercased.contains("uk") || lowercased.contains("united kingdom") { return "🇬🇧" }
        if lowercased.contains("japan") { return "🇯🇵" }
        if lowercased.contains("china") { return "🇨🇳" }
        return "🌍"
    }
}

struct TransportImpactCard: View {
    let sustainability: SustainabilityInfo
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "truck.box.fill")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.orange)
                
                Text("Transport Impact")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            VStack(spacing: 8) {
                HStack {
                    Text("Transport Distance")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text("\(Int(sustainability.transportDistance)) km")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.primary)
                }
                
                HStack {
                    Text("Transport Emissions")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(String(format: "%.2f kg CO₂", sustainability.carbonFootprint * 0.3))
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.primary)
                }
            }
        }
        .padding(20)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

struct RecommendationsCard: View {
    let sustainability: SustainabilityInfo
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "lightbulb.fill")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.yellow)
                
                Text("Sustainability Tips")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 12) {
                ForEach(sustainability.recommendations, id: \.self) { recommendation in
                    RecommendationRow(text: recommendation)
                }
            }
        }
        .padding(20)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

struct RecommendationRow: View {
    let text: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.green)
                .padding(.top, 2)
            
            Text(text)
                .font(.system(size: 14))
                .foregroundColor(.primary)
                .multilineTextAlignment(.leading)
            
            Spacer()
        }
    }
}

struct LoadingSustainabilityCard: View {
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Calculating sustainability score...")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.secondary)
        }
        .frame(height: 200)
        .frame(maxWidth: .infinity)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

struct NoSustainabilityDataCard: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.circle")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(.gray)
            
            Text("No sustainability data available")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.primary)
            
            Text("Sustainability information could not be calculated for this product.")
                .font(.system(size: 14))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(height: 200)
        .frame(maxWidth: .infinity)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

#Preview {
    ModernSustainabilityView(
        productName: "Organic Maple Syrup",
        origin: "Canada",
        barcode: "123456789",
        sustainabilityInfo: nil
    )
}
