//
//  Persistence.swift
//  IsitLocal
//
//  Created by <PERSON> on 2025-04-02.
//

import CoreData

class PersistenceController {
    static let shared = PersistenceController()

    static var preview: PersistenceController = {
        // Create an in-memory persistence controller for previews
        let controller = PersistenceController(inMemory: true)

        // Create sample data on a background context to avoid main thread issues
        let backgroundContext = controller.container.newBackgroundContext()
        backgroundContext.perform {
            // Create sample items
            for i in 0..<10 {
                let newItem = Item(context: backgroundContext)
                newItem.timestamp = Date()
                newItem.barcode = "SampleBarcode\(i)"
                newItem.productName = "Sample Product \(i)"
                newItem.origin = "Sample Origin \(i)"
            }

            // Save the context
            do {
                try backgroundContext.save()
            } catch {
                print("Preview data creation failed: \(error)")
            }
        }

        return controller
    }()

    let container: NSPersistentContainer

    init(inMemory: Bool = false) {
        container = NSPersistentContainer(name: "IsitLocal_") // Updated to match the model file name

        // Configure the persistent store
        if inMemory {
            container.persistentStoreDescriptions.first!.url = URL(fileURLWithPath: "/dev/null")
        }

        // Set options to improve performance
        let description = container.persistentStoreDescriptions.first
        description?.setOption(true as NSNumber, forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)
        description?.setOption(true as NSNumber, forKey: NSPersistentHistoryTrackingKey)

        // Configure view context
        container.viewContext.automaticallyMergesChangesFromParent = true
        container.viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        container.viewContext.shouldDeleteInaccessibleFaults = true

        // Load stores synchronously for initialization
        container.loadPersistentStores(completionHandler: { (storeDescription, error) in
            if let error = error as NSError? {
                // Log the error but don't crash
                print("Failed to load persistent stores: \(error), \(error.userInfo)")
            }
        })
    }

    // Simple initialization method that completes immediately
    func initializeAsync(completion: @escaping () -> Void) {
        // Call completion immediately
        completion()
    }
}
