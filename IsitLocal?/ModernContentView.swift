//
//  ModernContentView.swift
//  IsitLocal?
//
//  Created by Augment Agent on 2025-06-11.
//  Modern main content view with beautiful bottom navigation
//

import SwiftUI
import CoreData

struct ModernContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @StateObject private var appState = AppState()
    @State private var selectedTab: TabItem = .scan
    
    enum TabItem: String, CaseIterable {
        case scan = "Scan"
        case photo = "Photo"
        case insights = "Insights"
        case settings = "Settings"
        
        var icon: String {
            switch self {
            case .scan: return "barcode.viewfinder"
            case .photo: return "camera"
            case .insights: return "chart.bar"
            case .settings: return "gearshape"
            }
        }

        var activeIcon: String {
            switch self {
            case .scan: return "barcode.viewfinder"
            case .photo: return "camera.fill"
            case .insights: return "chart.bar.fill"
            case .settings: return "gearshape.fill"
            }
        }
    }
    
    var body: some View {
        ZStack {
            // Main Content
            VStack {
                switch selectedTab {
                case .scan:
                    ModernScanView()
                        .environmentObject(appState)
                        .environment(\.managedObjectContext, viewContext)
                        .transition(.opacity)
                    
                case .photo:
                    ImageBasedScanView()
                        .environment(\.managedObjectContext, viewContext)
                        .transition(.opacity)

                case .insights:
                    ModernInsightsView()
                        .environment(\.managedObjectContext, viewContext)
                        .transition(.opacity)
                    
                case .settings:
                    NavigationView {
                        ZStack {
                            // Background gradient to match other views
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(red: 0.4, green: 0.49, blue: 0.92),  // #667eea
                                    Color(red: 0.46, green: 0.29, blue: 0.64)  // #764ba2
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                            .ignoresSafeArea()

                            ScrollView {
                                VStack(spacing: 30) {
                                    // Header
                                    VStack(spacing: 16) {
                                        Text("Settings")
                                            .font(.system(size: 28, weight: .bold))
                                            .foregroundColor(.white)

                                        Text("Customize your IsitLocal experience")
                                            .font(.system(size: 16, weight: .light))
                                            .foregroundColor(.white.opacity(0.9))
                                    }
                                    .padding(.top, 40)
                                    .padding(.bottom, 20)

                                    VStack(spacing: 15) {
                                        SettingsCard(
                                            icon: "info.circle",
                                            title: "App Version",
                                            subtitle: "Version 1.0",
                                            action: {}
                                        )

                                        SettingsCard(
                                            icon: "hand.raised",
                                            title: "Privacy Policy",
                                            subtitle: "How we protect your data",
                                            action: {}
                                        )

                                        SettingsCard(
                                            icon: "questionmark.circle",
                                            title: "Support",
                                            subtitle: "Get help and contact us",
                                            action: {}
                                        )

                                        SettingsCard(
                                            icon: "brain.head.profile",
                                            title: "AI Assistant",
                                            subtitle: "Powered by IsitLocal AI",
                                            action: {}
                                        )

                                        SettingsCard(
                                            icon: "chart.line.uptrend.xyaxis",
                                            title: "Statistics",
                                            subtitle: "View detailed scanning statistics",
                                            action: {}
                                        )

                                        SettingsCard(
                                            icon: "person.crop.circle.badge.plus",
                                            title: "My Contributions",
                                            subtitle: "Products you've added to the database",
                                            action: {}
                                        )

                                        SettingsCard(
                                            icon: "wifi.slash",
                                            title: "Offline Mode",
                                            subtitle: "Access saved products without internet",
                                            action: {}
                                        )
                                    }
                                    .padding(.horizontal, 20)

                                    Spacer(minLength: 120)
                                }
                            }
                        }
                        .navigationBarHidden(true)
                    }
                    .transition(.opacity)
                }
            }
            .animation(.easeInOut(duration: 0.3), value: selectedTab)
            
            // Modern Bottom Navigation
            VStack {
                Spacer()
                ModernBottomNavigation(selectedTab: $selectedTab)
            }
        }
        .ignoresSafeArea(.keyboard, edges: .bottom)
    }
}

// MARK: - Modern Bottom Navigation
struct ModernBottomNavigation: View {
    @Binding var selectedTab: ModernContentView.TabItem
    
    var body: some View {
        HStack {
            ForEach(ModernContentView.TabItem.allCases, id: \.self) { tab in
                TabButton(
                    tab: tab,
                    isSelected: selectedTab == tab,
                    action: { 
                        withAnimation(.easeInOut(duration: 0.2)) {
                            selectedTab = tab
                        }
                    }
                )
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 24))
        .shadow(color: .black.opacity(0.1), radius: 20, x: 0, y: 10)
        .padding(.horizontal, 20)
        .padding(.bottom, 34)
    }
}

// MARK: - Tab Button
struct TabButton: View {
    let tab: ModernContentView.TabItem
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: isSelected ? tab.activeIcon : tab.icon)
                    .font(.system(size: 20, weight: isSelected ? .semibold : .medium))
                    .foregroundColor(isSelected ? .white : .secondary)
                    .frame(height: 24)
                
                Text(tab.rawValue)
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(isSelected ? .white : .secondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 8)
            .background(
                Group {
                    if isSelected {
                        LinearGradient(
                            colors: [Color(red: 0.4, green: 0.49, blue: 0.92), Color(red: 0.46, green: 0.29, blue: 0.64)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    } else {
                        Color.clear
                    }
                }
            )
            .clipShape(RoundedRectangle(cornerRadius: 12))
            .scaleEffect(isSelected ? 1.0 : 0.95)
            .animation(.easeInOut(duration: 0.2), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Modern Insights View (Placeholder)
struct ModernInsightsView: View {
    @Environment(\.managedObjectContext) private var viewContext
    
    // Fetch all items for statistics
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Item.timestamp, ascending: false)],
        animation: .default
    ) private var items: FetchedResults<Item>
    
    var localProductsCount: Int {
        items.filter { item in
            guard let origin = item.origin else { return false }
            return origin.lowercased().contains("canada") || origin.lowercased().contains("local")
        }.count
    }

    func getWeeklyCount() -> Int {
        let calendar = Calendar.current
        let now = Date()
        let oneWeekAgo = calendar.date(byAdding: .weekOfYear, value: -1, to: now) ?? now

        return items.filter { item in
            guard let timestamp = item.timestamp else { return false }
            return timestamp >= oneWeekAgo
        }.count
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient to match other views
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.4, green: 0.49, blue: 0.92),  // #667eea
                        Color(red: 0.46, green: 0.29, blue: 0.64)  // #764ba2
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()

                ScrollView {
                    VStack(spacing: 30) {
                        // Header
                        VStack(spacing: 16) {
                            Text("Your Insights")
                                .font(.system(size: 28, weight: .bold))
                                .foregroundColor(.white)

                            Text("Track your local shopping impact")
                                .font(.system(size: 16, weight: .light))
                                .foregroundColor(.white.opacity(0.9))
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 40)
                        
                        // Summary Cards
                        HStack(spacing: 16) {
                            SummaryCard(
                                number: "\(items.count)",
                                label: "Products Scanned",
                                change: "+\(getWeeklyCount()) this week",
                                isPositive: true
                            )
                            
                            SummaryCard(
                                number: "\(Int((Double(localProductsCount) / Double(max(items.count, 1))) * 100))%",
                                label: "Local Products",
                                change: "Great progress!",
                                isPositive: true
                            )
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, -60)
                        
                        // Insights List
                        VStack(spacing: 16) {
                            InsightRow(
                                icon: "location.fill",
                                iconColor: .green,
                                title: "Local Champion",
                                description: "You've chosen local products \(Int((Double(localProductsCount) / Double(max(items.count, 1))) * 100))% of the time!"
                            )
                            
                            InsightRow(
                                icon: "leaf.fill",
                                iconColor: .orange,
                                title: "Environmental Impact",
                                description: "Your local choices help reduce transportation emissions"
                            )
                            
                            InsightRow(
                                icon: "heart.fill",
                                iconColor: .pink,
                                title: "Health Focus",
                                description: "Keep scanning to discover healthier local alternatives"
                            )
                            
                            InsightRow(
                                icon: "dollarsign.circle.fill",
                                iconColor: .blue,
                                title: "Smart Shopping",
                                description: "Local products often provide better value and freshness"
                            )
                        }
                        .padding(.horizontal, 20)
                        
                        Spacer(minLength: 120)
                    }
                }
            }
            .navigationBarHidden(true)
        }
    }
}

// MARK: - Supporting Components
struct SummaryCard: View {
    let number: String
    let label: String
    let change: String
    let isPositive: Bool
    
    var body: some View {
        VStack(spacing: 8) {
            Text(number)
                .font(.system(size: 32, weight: .bold))
                .foregroundColor(Color(red: 0.4, green: 0.49, blue: 0.92))
            
            Text(label)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Text(change)
                .font(.system(size: 12, weight: .semibold))
                .foregroundColor(isPositive ? .green : .red)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 20)
        .background(
            LinearGradient(
                colors: [Color.blue.opacity(0.05), Color.purple.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color(.systemGray5), lineWidth: 1)
        )
    }
}

struct InsightRow: View {
    let icon: String
    let iconColor: Color
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 16) {
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            colors: [iconColor, iconColor.opacity(0.8)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 40, height: 40)
                
                Image(systemName: icon)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
        }
        .padding(16)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
}

// MARK: - Settings Card Component
struct SettingsCard: View {
    let icon: String
    let title: String
    let subtitle: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white.opacity(0.2))
                        .frame(width: 44, height: 44)

                    Image(systemName: icon)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)

                    Text(subtitle)
                        .font(.system(size: 14))
                        .foregroundColor(.white.opacity(0.8))
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.6))
            }
            .padding(20)
            .background(.ultraThinMaterial)
            .clipShape(RoundedRectangle(cornerRadius: 16))
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    ModernContentView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
