//
//  ProductDetailView.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-02.
//

import SwiftUI
import CoreData
import UIKit

struct ProductDetailView: View {
    let barcode: String
    let productName: String?
    let manufacturer: String?
    let ingredients: String?
    let category: String?
    let origin: String?
    let imageUrl: String?

    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.presentationMode) private var presentationMode
    @State private var showingNutritionView = false
    @State private var showingSustainabilityView = false
    @State private var showingComparisonView = false
    @State private var showingContributionView = false

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Product image
                if let imageUrl = imageUrl {
                    RemoteImageView(url: URL(string: imageUrl), height: 250)
                        .padding(.horizontal)
                        .shadow(color: Color.black.opacity(0.2), radius: 5, x: 0, y: 2)
                } else {
                    Image(systemName: "photo")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(height: 250)
                        .foregroundColor(.gray)
                        .padding(.horizontal)
                        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                }

                // Enhanced product name and origin
                VStack(spacing: 10) {
                    Text(productName ?? "Unknown Product")
                        .font(.title)
                        .fontWeight(.bold)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)

                    if let origin = origin {
                        HStack {
                            Image(systemName: "globe")
                                .font(.title3)
                                .foregroundColor(.blue)
                            Text("Origin: \(origin)")
                                .font(.title3)
                                .fontWeight(.semibold)
                        }
                        .padding(.vertical, 10)
                        .padding(.horizontal, 25)
                        .background(Color.green.opacity(0.2))
                        .clipShape(RoundedRectangle(cornerRadius: 15))
                        .shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 1)
                    }
                }
                .padding(.bottom, 15)

                // Product details
                VStack(alignment: .leading, spacing: 15) {
                    DetailSection(title: "Product Information", content: {
                        VStack(alignment: .leading, spacing: 4) { // Wrap content in VStack
                            DetailRow(label: "Barcode", value: barcode, icon: "barcode")
                            if let manufacturer = manufacturer {
                                DetailRow(label: "Manufacturer", value: manufacturer, icon: "building.2")
                            }
                            if let category = category {
                                DetailRow(label: "Category", value: category, icon: "tag")
                            }
                        }
                    })

                    if let ingredients = ingredients, !ingredients.isEmpty {
                        DetailSection(title: "Ingredients", content: {
                            HStack(alignment: .top) {
                                Image(systemName: "list.bullet")
                                    .foregroundColor(.blue)
                                    .frame(width: 20)

                                Text(ingredients)
                                    .font(.body)
                                    .foregroundColor(.primary)
                                    .fixedSize(horizontal: false, vertical: true)
                            }
                            .padding(.vertical, 4)
                        })
                    }
                }
                .padding(.horizontal)

                // Additional features
                VStack(spacing: 15) {
                    // NEW: Gemini AI Enhancement Button
                    NavigationLink {
                        GeminiEnhancedProductView(
                            productName: productName ?? "Unknown Product",
                            manufacturer: manufacturer,
                            barcode: barcode,
                            category: category,
                            origin: origin
                        )
                    } label: {
                        HStack {
                            Image(systemName: "brain.head.profile")
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(width: 36, height: 36)
                                .background(Color.orange)
                                .clipShape(Circle())

                            VStack(alignment: .leading, spacing: 2) {
                                Text("AI Enhanced Info")
                                    .font(.headline)
                                    .foregroundColor(.primary)

                                Text("Powered by Gemini AI")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()

                            Image(systemName: "chevron.right")
                                .foregroundColor(.secondary)
                        }
                        .padding(.vertical, 12)
                        .padding(.horizontal, 16)
                        .background(Color.white)
                        .cornerRadius(12)
                        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                    }

                    FeatureButton(title: "Nutrition Information", icon: "heart.fill", color: .red) {
                        showingNutritionView = true
                    }

                    FeatureButton(title: "Sustainability Score", icon: "leaf.fill", color: .green) {
                        showingSustainabilityView = true
                    }

                    FeatureButton(title: "Compare Products", icon: "arrow.left.arrow.right", color: .blue) {
                        showingComparisonView = true
                    }

                    FeatureButton(title: "Contribute Data", icon: "person.fill.badge.plus", color: .purple) {
                        showingContributionView = true
                    }

                    // Return to scanner button
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        HStack {
                            Image(systemName: "barcode.viewfinder")
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(width: 36, height: 36)
                                .background(Color.green)
                                .clipShape(Circle())

                            Text("Scan Another Product")
                                .font(.headline)
                                .foregroundColor(.primary)

                            Spacer()

                            Image(systemName: "chevron.right")
                                .foregroundColor(.secondary)
                        }
                        .padding(.vertical, 12)
                        .padding(.horizontal, 16)
                        .background(Color.white)
                        .cornerRadius(12)
                        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                    }
                }
                .padding(.horizontal)
                .padding(.top, 10)

                Spacer()
            }
            .padding(.vertical)
        }
        .background(Color.gray.opacity(0.1).ignoresSafeArea())
        .navigationTitle("Product Details")
        .navigationBarItems(trailing: Button(action: {
            presentationMode.wrappedValue.dismiss()
        }) {
            Image(systemName: "xmark.circle.fill")
                .font(.title2)
                .foregroundColor(.gray)
        })
        .sheet(isPresented: $showingNutritionView) {
            NutritionView(barcode: barcode)
                .environment(\.managedObjectContext, viewContext)
        }
        .sheet(isPresented: $showingSustainabilityView) {
            if let origin = origin, let productName = productName {
                SustainabilityView(barcode: barcode, origin: origin, productName: productName)
                    .environment(\.managedObjectContext, viewContext)
            }
        }
        .sheet(isPresented: $showingComparisonView) {
            ProductComparisonView()
                .environment(\.managedObjectContext, viewContext)
        }
        .sheet(isPresented: $showingContributionView) {
            UserContributionView(barcode: barcode, productName: productName)
                .environment(\.managedObjectContext, viewContext)
        }
    }
}

struct DetailSection<Content: View>: View {
    let title: String
    let content: () -> Content

    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text(title)
                .font(.headline)
                .foregroundColor(.secondary)

            content()
                .padding(.leading, 5)

            Divider()
                .padding(.top, 5)
        }
    }
}

struct DetailRow: View {
    let label: String
    let value: String
    let icon: String

    init(label: String, value: String, icon: String = "info.circle") {
        self.label = label
        self.value = value
        self.icon = icon
    }

    var body: some View {
        HStack(alignment: .top) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 20)

            Text(label + ":")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .frame(width: 100, alignment: .leading)

            Text(value)
                .font(.subheadline)
                .foregroundColor(.primary)
                .fixedSize(horizontal: false, vertical: true)

            Spacer()
        }
        .padding(.vertical, 4)
    }
}

struct FeatureButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(width: 36, height: 36)
                    .background(color)
                    .clipShape(Circle())

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(.primary)

                    Text("Tap to view")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        }
    }
}
