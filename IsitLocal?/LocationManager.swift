//
//  LocationManager.swift
//  IsitLocal
//
//  Created by <PERSON> on 2025-04-02.
//

import Foundation
import CoreLocation

class LocationManager: NSObject, ObservableObject, CLLocationManagerDelegate {
    private let locationManager = CLLocationManager()
    @Published var userCountry: String?
    @Published var locationError: Error?
    @Published var hasLocationError: Bool = false
    @Published var authorizationStatus: CLAuthorizationStatus?

    override init() {
        super.init()
        // Set up location manager with minimal configuration for faster launch
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyKilometer
        authorizationStatus = locationManager.authorizationStatus
    }

    func requestLocation() {
        // Check the current authorization status without excessive logging
        let status = locationManager.authorizationStatus
        authorizationStatus = status

        switch status {
        case .notDetermined:
            // If status is not determined, request authorization
            locationManager.requestWhenInUseAuthorization()
        case .authorizedWhenInUse, .authorizedAlways:
            // If already authorized, request location
            locationManager.requestLocation()
        case .denied, .restricted:
            // If denied or restricted, set the error state
            hasLocationError = true
            locationError = NSError(domain: "LocationManager", code: 1, userInfo: [NSLocalizedDescriptionKey: "Location access denied"])
            userCountry = "United States" // Default to US
        @unknown default:
            hasLocationError = true
            userCountry = "United States" // Default to US
        }
    }

    func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
        // Handle authorization changes on the main thread
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            let status = manager.authorizationStatus
            self.authorizationStatus = status

            switch status {
            case .authorizedWhenInUse, .authorizedAlways:
                // If authorization is granted, request location
                self.locationManager.requestLocation()
                self.hasLocationError = false
            case .denied, .restricted:
                // If authorization is denied, set error state
                self.hasLocationError = true
                self.userCountry = "United States" // Default to US
            case .notDetermined:
                // Do nothing, wait for user to respond to the prompt
                break
            @unknown default:
                self.hasLocationError = true
                self.userCountry = "United States" // Default to US
            }
        }
    }

    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        // Handle location updates on the main thread
        DispatchQueue.main.async { [weak self] in
            guard let self = self, let location = locations.last else { return }

            let geocoder = CLGeocoder()
            geocoder.reverseGeocodeLocation(location) { [weak self] (placemarks, error) in
                // Ensure we're on the main thread for UI updates
                DispatchQueue.main.async {
                    if let error = error {
                        self?.locationError = error
                        self?.hasLocationError = true
                        self?.userCountry = "United States" // Default to US if geocoding fails
                        return
                    }

                    self?.hasLocationError = false
                    self?.locationError = nil
                    if let country = placemarks?.first?.country {
                        self?.userCountry = country
                    } else {
                        self?.userCountry = "United States" // Default to US if country not found
                    }
                }
            }
        }
    }

    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        // Handle location errors on the main thread
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            self.locationError = error
            self.hasLocationError = true
            self.userCountry = "United States" // Default to US on error
        }
    }
}
