<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="1" systemVersion="11A491" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithCloudKit="true" userDefinedModelVersionIdentifier="">
    <entity name="Item" representedClassName="Item" syncable="YES" codeGenerationType="class">
        <attribute name="barcode" optional="YES" attributeType="String"/>
        <attribute name="category" optional="YES" attributeType="String"/>
        <attribute name="isFavorite" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="notes" optional="YES" attributeType="String"/>
        <attribute name="origin" optional="YES" attributeType="String"/>
        <attribute name="productName" optional="YES" attributeType="String"/>
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
    </entity>
    <entity name="NutritionInfo" representedClassName="NutritionInfo" syncable="YES" codeGenerationType="class">
        <attribute name="barcode" optional="YES" attributeType="String"/>
        <attribute name="calories" optional="YES" attributeType="String"/>
        <attribute name="carbohydrates" optional="YES" attributeType="String"/>
        <attribute name="fat" optional="YES" attributeType="String"/>
        <attribute name="fiber" optional="YES" attributeType="String"/>
        <attribute name="lastUpdated" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="protein" optional="YES" attributeType="String"/>
        <attribute name="servingSize" optional="YES" attributeType="String"/>
        <attribute name="sodium" optional="YES" attributeType="String"/>
        <attribute name="sugar" optional="YES" attributeType="String"/>
    </entity>
    <entity name="OfflineProduct" representedClassName="OfflineProduct" syncable="YES" codeGenerationType="class">
        <attribute name="barcode" optional="YES" attributeType="String"/>
        <attribute name="origin" optional="YES" attributeType="String"/>
        <attribute name="productName" optional="YES" attributeType="String"/>
        <attribute name="savedDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
    </entity>
    <entity name="ProductCache" representedClassName="ProductCache" syncable="YES" codeGenerationType="class">
        <attribute name="barcode" optional="YES" attributeType="String"/>
        <attribute name="category" optional="YES" attributeType="String"/>
        <attribute name="imageUrl" optional="YES" attributeType="String"/>
        <attribute name="ingredients" optional="YES" attributeType="String"/>
        <attribute name="lastUpdated" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="manufacturer" optional="YES" attributeType="String"/>
        <attribute name="origin" optional="YES" attributeType="String"/>
        <attribute name="productName" optional="YES" attributeType="String"/>
    </entity>
    <entity name="ProductComparison" representedClassName="ProductComparison" syncable="YES" codeGenerationType="class">
        <attribute name="comparisonDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="product1Name" optional="YES" attributeType="String"/>
        <attribute name="product1Origin" optional="YES" attributeType="String"/>
        <attribute name="product2Name" optional="YES" attributeType="String"/>
        <attribute name="product2Origin" optional="YES" attributeType="String"/>
        <attribute name="transportImpact1" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="transportImpact2" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
    </entity>
    <entity name="SustainabilityInfo" representedClassName="SustainabilityInfo" syncable="YES" codeGenerationType="class">
        <attribute name="barcode" optional="YES" attributeType="String"/>
        <attribute name="calculationDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="carbonFootprint" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="ecoScore" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="origin" optional="YES" attributeType="String"/>
        <attribute name="productType" optional="YES" attributeType="String"/>
        <attribute name="transportImpact" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
    </entity>
    <entity name="UserContribution" representedClassName="UserContribution" syncable="YES" codeGenerationType="class">
        <attribute name="barcode" optional="YES" attributeType="String"/>
        <attribute name="notes" optional="YES" attributeType="String"/>
        <attribute name="origin" optional="YES" attributeType="String"/>
        <attribute name="productName" optional="YES" attributeType="String"/>
        <attribute name="status" optional="YES" attributeType="String"/>
        <attribute name="submissionDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
    </entity>
    <elements>
        <element name="Item" positionX="-63" positionY="-18" width="128" height="134"/>
        <element name="NutritionInfo" positionX="-54" positionY="45" width="128" height="179"/>
        <element name="OfflineProduct" positionX="-36" positionY="108" width="128" height="89"/>
        <element name="ProductCache" positionX="-54" positionY="36" width="128" height="134"/>
        <element name="ProductComparison" positionX="-18" positionY="126" width="128" height="134"/>
        <element name="SustainabilityInfo" positionX="0" positionY="144" width="128" height="134"/>
        <element name="UserContribution" positionX="18" positionY="162" width="128" height="119"/>
    </elements>
</model>
