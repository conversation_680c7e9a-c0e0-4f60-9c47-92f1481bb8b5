//
//  SimpleScannerView.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-02.
//

import SwiftUI
import AVFoundation

struct SimpleScannerView: UIViewControllerRepresentable {
    var completion: (Result<String, Error>) -> Void

    func makeUIViewController(context: Context) -> ScannerViewController {
        let viewController = ScannerViewController()
        viewController.completion = completion
        return viewController
    }

    func updateUIViewController(_ uiViewController: ScannerViewController, context: Context) {}
                // Simulate a successful scan
                didFindCode("123456789012")
            }
            .padding(10)
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(8)
            #else
            // Real scanner for device
            if isShowingScanner {
                ScannerViewController(didFindCode: didFindCode)
                    .edgesIgnoringSafeArea(.all)
            }
            #endif
        }
        .onAppear {
            // Start the scanner when the view appears
            #if !targetEnvironment(simulator)
            isShowingScanner = true
            #endif
        }
    }
}

// Scanner view controller for real device
struct ScannerViewController: UIViewControllerRepresentable {
    var didFindCode: (String) -> Void

    func makeUIViewController(context: Context) -> UIViewController {
        let viewController = ScannerVC()
        viewController.delegate = context.coordinator
        return viewController
    }

    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {
        // No updates needed
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, ScannerVCDelegate {
        let parent: ScannerViewController

        init(_ parent: ScannerViewController) {
            self.parent = parent
        }

        func didFindCode(_ code: String) {
            parent.didFindCode(code)
        }
    }
}

protocol ScannerVCDelegate: AnyObject {
    func didFindCode(_ code: String)
}

class ScannerVC: UIViewController, AVCaptureMetadataOutputObjectsDelegate {
    weak var delegate: ScannerVCDelegate?

    var captureSession: AVCaptureSession?
    var previewLayer: AVCaptureVideoPreviewLayer?

    override func viewDidLoad() {
        super.viewDidLoad()

        // Set the view background to black
        view.backgroundColor = .black

        // Setup camera
        setupCamera()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        // Start the session if it's not running
        if captureSession?.isRunning == false {
            captureSession?.startRunning()
        }
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)

        // Stop the session if it's running
        if captureSession?.isRunning == true {
            captureSession?.stopRunning()
        }
    }

    private func setupCamera() {
        // Create a capture session
        let session = AVCaptureSession()
        captureSession = session

        // Get the default video device
        guard let videoCaptureDevice = AVCaptureDevice.default(for: .video) else {
            print("No camera available")
            return
        }

        // Create a device input
        let videoInput: AVCaptureDeviceInput
        do {
            videoInput = try AVCaptureDeviceInput(device: videoCaptureDevice)
        } catch {
            print("Failed to create video input: \(error)")
            return
        }

        // Add the input to the session
        if session.canAddInput(videoInput) {
            session.addInput(videoInput)
        } else {
            print("Cannot add video input")
            return
        }

        // Create a metadata output
        let metadataOutput = AVCaptureMetadataOutput()

        // Add the output to the session
        if session.canAddOutput(metadataOutput) {
            session.addOutput(metadataOutput)

            // Configure the output
            metadataOutput.setMetadataObjectsDelegate(self, queue: DispatchQueue.main)
            metadataOutput.metadataObjectTypes = [.ean13, .ean8, .upce, .qr, .code128, .code39, .pdf417]
        } else {
            print("Cannot add metadata output")
            return
        }

        // Create a preview layer
        let previewLayer = AVCaptureVideoPreviewLayer(session: session)
        self.previewLayer = previewLayer
        previewLayer.frame = view.layer.bounds
        previewLayer.videoGravity = .resizeAspectFill
        view.layer.addSublayer(previewLayer)

        // Start the session
        session.startRunning()
    }

    func metadataOutput(_ output: AVCaptureMetadataOutput, didOutput metadataObjects: [AVMetadataObject], from connection: AVCaptureConnection) {
        // Stop scanning
        captureSession?.stopRunning()

        // Process the first metadata object
        if let metadataObject = metadataObjects.first as? AVMetadataMachineReadableCodeObject,
           let stringValue = metadataObject.stringValue {

            // Log the scanned code for debugging
            print("Scanned code: \(stringValue)")

            // Notify the delegate
            delegate?.didFindCode(stringValue)
        }
    }
}

class ScannerViewController: UIViewController, AVCaptureMetadataOutputObjectsDelegate {
    weak var delegate: ScannerViewControllerDelegate?

    var captureSession: AVCaptureSession?
    var previewLayer: AVCaptureVideoPreviewLayer?

    override func viewDidLoad() {
        super.viewDidLoad()

        // Set the view background to black to avoid white flash
        view.backgroundColor = .black

        #if targetEnvironment(simulator)
        // Return a dummy code in the simulator
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            self?.delegate?.didFindCode("123456789012")
        }
        #else
        // Check camera permissions
        checkCameraPermission()
        #endif
    }

    private func checkCameraPermission() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            // Camera permission already granted
            DispatchQueue.main.async { [weak self] in
                self?.setupCamera()
            }
        case .notDetermined:
            // Request camera permission
            AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
                if granted {
                    DispatchQueue.main.async {
                        self?.setupCamera()
                    }
                } else {
                    DispatchQueue.main.async {
                        self?.delegate?.didFindCode("CAMERA_PERMISSION_DENIED")
                    }
                }
            }
        case .denied, .restricted:
            // Camera permission denied
            DispatchQueue.main.async { [weak self] in
                self?.delegate?.didFindCode("CAMERA_PERMISSION_DENIED")
            }
        @unknown default:
            // Unknown status
            DispatchQueue.main.async { [weak self] in
                self?.delegate?.didFindCode("CAMERA_PERMISSION_UNKNOWN")
            }
        }
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        #if !targetEnvironment(simulator)
        // Start the session on the main thread
        DispatchQueue.main.async { [weak self] in
            if self?.captureSession?.isRunning == false {
                self?.captureSession?.startRunning()
            }
        }
        #endif
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)

        #if !targetEnvironment(simulator)
        if captureSession?.isRunning == true {
            captureSession?.stopRunning()
        }
        #endif
    }

    private func setupCamera() {
        // Create a capture session
        let session = AVCaptureSession()
        captureSession = session

        // Set session preset for better quality
        session.sessionPreset = .high

        // Get the default video device
        guard let videoCaptureDevice = AVCaptureDevice.default(for: .video) else {
            print("No camera available")
            // Send a dummy code to avoid hanging
            DispatchQueue.main.async { [weak self] in
                self?.delegate?.didFindCode("NO_CAMERA_AVAILABLE")
            }
            return
        }

        // Create a device input
        let videoInput: AVCaptureDeviceInput
        do {
            videoInput = try AVCaptureDeviceInput(device: videoCaptureDevice)
        } catch {
            print("Failed to create video input: \(error)")
            // Send a dummy code to avoid hanging
            DispatchQueue.main.async { [weak self] in
                self?.delegate?.didFindCode("CAMERA_INIT_ERROR")
            }
            return
        }

        // Add the input to the session
        if session.canAddInput(videoInput) {
            session.addInput(videoInput)
        } else {
            print("Cannot add video input")
            // Send a dummy code to avoid hanging
            DispatchQueue.main.async { [weak self] in
                self?.delegate?.didFindCode("CAMERA_CONFIG_ERROR")
            }
            return
        }

        // Create a metadata output
        let metadataOutput = AVCaptureMetadataOutput()

        // Add the output to the session
        if session.canAddOutput(metadataOutput) {
            session.addOutput(metadataOutput)

            // Configure the output
            metadataOutput.setMetadataObjectsDelegate(self, queue: DispatchQueue.main)
            metadataOutput.metadataObjectTypes = [.ean13, .ean8, .upce, .qr, .code128, .code39, .pdf417]
        } else {
            print("Cannot add metadata output")
            // Send a dummy code to avoid hanging
            DispatchQueue.main.async { [weak self] in
                self?.delegate?.didFindCode("METADATA_CONFIG_ERROR")
            }
            return
        }

        // Add the preview layer on the main thread
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            let previewLayer = AVCaptureVideoPreviewLayer(session: session)
            self.previewLayer = previewLayer
            previewLayer.frame = self.view.layer.bounds
            previewLayer.videoGravity = .resizeAspectFill
            self.view.layer.addSublayer(previewLayer)

            // Start the session after a short delay
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                self?.captureSession?.startRunning()
            }
        }
    }

    func metadataOutput(_ output: AVCaptureMetadataOutput, didOutput metadataObjects: [AVMetadataObject], from connection: AVCaptureConnection) {
        // Make sure we have at least one metadata object
        guard !metadataObjects.isEmpty else { return }

        // Stop scanning to prevent multiple scans
        captureSession?.stopRunning()

        // Process the first metadata object
        if let metadataObject = metadataObjects.first as? AVMetadataMachineReadableCodeObject,
           let stringValue = metadataObject.stringValue {

            // Log the scanned code for debugging
            print("Scanned code: \(stringValue)")

            // Notify the delegate on the main thread
            DispatchQueue.main.async { [weak self] in
                self?.delegate?.didFindCode(stringValue)
            }
        } else {
            // If we couldn't read the code, send a dummy code
            DispatchQueue.main.async { [weak self] in
                self?.delegate?.didFindCode("UNREADABLE_CODE")
            }
        }
    }
}
