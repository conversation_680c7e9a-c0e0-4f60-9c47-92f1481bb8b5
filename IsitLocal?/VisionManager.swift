import Foundation
import Vision
import UIKit
import AVFoundation

// MARK: - Vision Analysis Results
struct VisionAnalysisResult {
    let barcodes: [String]
    let nutritionText: [String]
    let ingredientText: [String]
    let confidence: Float
    let detectedLanguage: String?
}

struct NutritionLabelData {
    let calories: String?
    let protein: String?
    let carbohydrates: String?
    let fat: String?
    let fiber: String?
    let sugar: String?
    let sodium: String?
    let servingSize: String?
    let ingredients: String?
}

// MARK: - Vision Manager for Enhanced Product Scanning
class VisionManager: ObservableObject {
    static let shared = VisionManager()
    
    private init() {}
    
    // MARK: - Enhanced Barcode Detection
    func detectBarcodes(in image: UIImage) async -> [String] {
        return await withCheckedContinuation { continuation in
            guard let cgImage = image.cgImage else {
                continuation.resume(returning: [])
                return
            }
            
            let request = VNDetectBarcodesRequest { request, error in
                if let error = error {
                    print("❌ Barcode detection error: \(error)")
                    continuation.resume(returning: [])
                    return
                }
                
                let barcodes = request.results?.compactMap { result in
                    (result as? VNBarcodeObservation)?.payloadStringValue
                } ?? []
                
                print("✅ Detected \(barcodes.count) barcodes: \(barcodes)")
                continuation.resume(returning: barcodes)
            }
            
            // Configure for multiple barcode types
            request.symbologies = [
                .ean8, .ean13, .upce, .upca,
                .code128, .code39, .code93,
                .qr, .dataMatrix, .pdf417
            ]
            
            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
            
            do {
                try handler.perform([request])
            } catch {
                print("❌ Failed to perform barcode detection: \(error)")
                continuation.resume(returning: [])
            }
        }
    }
    
    // MARK: - OCR Text Recognition for Nutrition Labels
    func recognizeText(in image: UIImage) async -> [String] {
        return await withCheckedContinuation { continuation in
            guard let cgImage = image.cgImage else {
                continuation.resume(returning: [])
                return
            }
            
            let request = VNRecognizeTextRequest { request, error in
                if let error = error {
                    print("❌ Text recognition error: \(error)")
                    continuation.resume(returning: [])
                    return
                }
                
                let recognizedText = request.results?.compactMap { result in
                    (result as? VNRecognizedTextObservation)?.topCandidates(1).first?.string
                } ?? []
                
                print("✅ Recognized \(recognizedText.count) text elements")
                continuation.resume(returning: recognizedText)
            }
            
            // Configure for accurate text recognition
            request.recognitionLevel = .accurate
            request.recognitionLanguages = ["en-US", "en-CA", "fr-CA"]
            request.usesLanguageCorrection = true
            
            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
            
            do {
                try handler.perform([request])
            } catch {
                print("❌ Failed to perform text recognition: \(error)")
                continuation.resume(returning: [])
            }
        }
    }
    
    // MARK: - Comprehensive Vision Analysis
    func analyzeProductImage(_ image: UIImage) async -> VisionAnalysisResult {
        print("🔍 Starting comprehensive vision analysis...")
        
        async let barcodes = detectBarcodes(in: image)
        async let textElements = recognizeText(in: image)
        
        let detectedBarcodes = await barcodes
        let recognizedText = await textElements
        
        // Parse nutrition and ingredient information
        let nutritionText = extractNutritionInfo(from: recognizedText)
        let ingredientText = extractIngredientInfo(from: recognizedText)
        
        // Calculate confidence based on detection success
        let confidence = calculateConfidence(
            barcodes: detectedBarcodes,
            nutritionText: nutritionText,
            ingredientText: ingredientText
        )
        
        let result = VisionAnalysisResult(
            barcodes: detectedBarcodes,
            nutritionText: nutritionText,
            ingredientText: ingredientText,
            confidence: confidence,
            detectedLanguage: "en"
        )
        
        print("✅ Vision analysis complete - Confidence: \(confidence)")
        return result
    }
    
    // MARK: - Parse Nutrition Label Data
    func parseNutritionLabel(_ image: UIImage) async -> NutritionLabelData {
        let recognizedText = await recognizeText(in: image)
        return extractNutritionData(from: recognizedText)
    }
    
    // MARK: - Private Helper Methods
    private func extractNutritionInfo(from text: [String]) -> [String] {
        let nutritionKeywords = [
            "calories", "protein", "carbohydrate", "fat", "fiber",
            "sugar", "sodium", "cholesterol", "vitamin", "mineral",
            "serving size", "servings per", "total fat", "saturated fat",
            "trans fat", "dietary fiber", "total sugars", "added sugars"
        ]
        
        return text.filter { line in
            let lowercased = line.lowercased()
            return nutritionKeywords.contains { keyword in
                lowercased.contains(keyword)
            }
        }
    }
    
    private func extractIngredientInfo(from text: [String]) -> [String] {
        let ingredientKeywords = [
            "ingredients", "contains", "allergens", "may contain",
            "made with", "organic", "natural", "artificial"
        ]
        
        return text.filter { line in
            let lowercased = line.lowercased()
            return ingredientKeywords.contains { keyword in
                lowercased.contains(keyword)
            } || line.count > 50 // Long lines likely to be ingredient lists
        }
    }
    
    private func calculateConfidence(barcodes: [String], nutritionText: [String], ingredientText: [String]) -> Float {
        var confidence: Float = 0.0
        
        // Barcode detection adds significant confidence
        if !barcodes.isEmpty {
            confidence += 0.4
        }
        
        // Nutrition text detection
        if !nutritionText.isEmpty {
            confidence += 0.3
        }
        
        // Ingredient text detection
        if !ingredientText.isEmpty {
            confidence += 0.2
        }
        
        // Base confidence for any text detection
        if !nutritionText.isEmpty || !ingredientText.isEmpty {
            confidence += 0.1
        }
        
        return min(confidence, 1.0)
    }
    
    private func extractNutritionData(from text: [String]) -> NutritionLabelData {
        var calories: String?
        var protein: String?
        var carbohydrates: String?
        var fat: String?
        var fiber: String?
        var sugar: String?
        var sodium: String?
        var servingSize: String?
        var ingredients: String?
        
        for line in text {
            let lowercased = line.lowercased()
            
            // Extract calories
            if lowercased.contains("calories") {
                calories = extractNumber(from: line, after: "calories")
            }
            
            // Extract protein
            if lowercased.contains("protein") {
                protein = extractNumber(from: line, after: "protein")
            }
            
            // Extract carbohydrates
            if lowercased.contains("carbohydrate") || lowercased.contains("carbs") {
                carbohydrates = extractNumber(from: line, after: ["carbohydrate", "carbs"])
            }
            
            // Extract fat
            if lowercased.contains("total fat") || (lowercased.contains("fat") && !lowercased.contains("saturated")) {
                fat = extractNumber(from: line, after: "fat")
            }
            
            // Extract fiber
            if lowercased.contains("fiber") || lowercased.contains("fibre") {
                fiber = extractNumber(from: line, after: ["fiber", "fibre"])
            }
            
            // Extract sugar
            if lowercased.contains("sugar") {
                sugar = extractNumber(from: line, after: "sugar")
            }
            
            // Extract sodium
            if lowercased.contains("sodium") {
                sodium = extractNumber(from: line, after: "sodium")
            }
            
            // Extract serving size
            if lowercased.contains("serving size") || lowercased.contains("serving") {
                servingSize = line.trimmingCharacters(in: .whitespacesAndNewlines)
            }
            
            // Extract ingredients
            if lowercased.contains("ingredients:") {
                ingredients = line.replacingOccurrences(of: "ingredients:", with: "", options: .caseInsensitive)
                    .trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }
        
        return NutritionLabelData(
            calories: calories,
            protein: protein,
            carbohydrates: carbohydrates,
            fat: fat,
            fiber: fiber,
            sugar: sugar,
            sodium: sodium,
            servingSize: servingSize,
            ingredients: ingredients
        )
    }
    
    private func extractNumber(from text: String, after keyword: String) -> String? {
        return extractNumber(from: text, after: [keyword])
    }
    
    private func extractNumber(from text: String, after keywords: [String]) -> String? {
        let lowercased = text.lowercased()
        
        for keyword in keywords {
            if let range = lowercased.range(of: keyword) {
                let afterKeyword = String(text[range.upperBound...])
                
                // Extract number with optional decimal and unit
                let pattern = #"(\d+(?:\.\d+)?)\s*([a-zA-Z]*)"#
                if let regex = try? NSRegularExpression(pattern: pattern),
                   let match = regex.firstMatch(in: afterKeyword, range: NSRange(afterKeyword.startIndex..., in: afterKeyword)) {
                    
                    if let numberRange = Range(match.range(at: 1), in: afterKeyword),
                       let unitRange = Range(match.range(at: 2), in: afterKeyword) {
                        let number = String(afterKeyword[numberRange])
                        let unit = String(afterKeyword[unitRange])
                        return unit.isEmpty ? number : "\(number) \(unit)"
                    }
                }
            }
        }
        
        return nil
    }
}
