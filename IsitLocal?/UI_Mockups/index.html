<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IsitLocal? - New UI Design Mockups</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .logo {
            font-size: 48px;
            font-weight: 800;
            margin-bottom: 16px;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }
        
        .tagline {
            font-size: 24px;
            opacity: 0.9;
            font-weight: 300;
            margin-bottom: 12px;
        }
        
        .subtitle {
            font-size: 18px;
            opacity: 0.8;
            font-weight: 400;
        }
        
        .mockups-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .mockup-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            color: #333;
            text-decoration: none;
        }
        
        .mockup-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
        }
        
        .mockup-icon {
            font-size: 64px;
            margin-bottom: 20px;
            display: block;
        }
        
        .mockup-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 12px;
            color: #2d3436;
        }
        
        .mockup-description {
            font-size: 16px;
            color: #636e72;
            line-height: 1.5;
            margin-bottom: 20px;
        }
        
        .mockup-features {
            list-style: none;
            text-align: left;
            margin-bottom: 20px;
        }
        
        .mockup-features li {
            font-size: 14px;
            color: #636e72;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .mockup-features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #00b894;
            font-weight: bold;
        }
        
        .view-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .view-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }
        
        .features-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            backdrop-filter: blur(10px);
        }
        
        .features-title {
            font-size: 32px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .feature-item {
            text-align: center;
            padding: 20px;
        }
        
        .feature-icon {
            font-size: 48px;
            margin-bottom: 16px;
            display: block;
        }
        
        .feature-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .feature-description {
            font-size: 16px;
            opacity: 0.9;
            line-height: 1.5;
        }
        
        .footer {
            text-align: center;
            padding: 40px 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .footer-text {
            font-size: 16px;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .mockups-grid {
                grid-template-columns: 1fr;
            }
            
            .logo {
                font-size: 36px;
            }
            
            .tagline {
                font-size: 20px;
            }
            
            .subtitle {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">IsitLocal?</div>
            <div class="tagline">New UI Design Mockups</div>
            <div class="subtitle">Beautiful, Modern, Mobile-First Interface</div>
        </div>
        
        <!-- Mockups Grid -->
        <div class="mockups-grid">
            <a href="01_main_scan_screen.html" class="mockup-card">
                <span class="mockup-icon">📱</span>
                <div class="mockup-title">Main Scan Screen</div>
                <div class="mockup-description">
                    The primary interface where users scan barcodes with a beautiful, intuitive design.
                </div>
                <ul class="mockup-features">
                    <li>Gradient background with glassmorphism</li>
                    <li>Animated scanner frame</li>
                    <li>Quick stats dashboard</li>
                    <li>Modern bottom navigation</li>
                    <li>Floating AI button</li>
                </ul>
                <span class="view-btn">View Mockup</span>
            </a>
            
            <a href="02_product_detail_screen.html" class="mockup-card">
                <span class="mockup-icon">📦</span>
                <div class="mockup-title">Product Detail Screen</div>
                <div class="mockup-description">
                    Comprehensive product information with enhanced visual hierarchy and feature cards.
                </div>
                <ul class="mockup-features">
                    <li>Hero product image section</li>
                    <li>Feature cards with gradients</li>
                    <li>AI Enhanced Info prominence</li>
                    <li>Clean information layout</li>
                    <li>Action buttons at bottom</li>
                </ul>
                <span class="view-btn">View Mockup</span>
            </a>
            
            <a href="03_favorites_screen.html" class="mockup-card">
                <span class="mockup-icon">⭐</span>
                <div class="mockup-title">Favorites Screen</div>
                <div class="mockup-description">
                    Beautiful favorites management with search, filtering, and enhanced product cards.
                </div>
                <ul class="mockup-features">
                    <li>Search and filter functionality</li>
                    <li>Card-based product layout</li>
                    <li>Quick action buttons</li>
                    <li>Badge system for categories</li>
                    <li>Swipe actions support</li>
                </ul>
                <span class="view-btn">View Mockup</span>
            </a>
            
            <a href="04_ai_enhanced_screen.html" class="mockup-card">
                <span class="mockup-icon">🧠</span>
                <div class="mockup-title">AI Enhanced Info</div>
                <div class="mockup-description">
                    Showcase of Gemini AI-powered insights with beautiful information cards and validation.
                </div>
                <ul class="mockup-features">
                    <li>AI-powered insights cards</li>
                    <li>Confidence scoring display</li>
                    <li>Local alternatives section</li>
                    <li>Data validation indicators</li>
                    <li>Refresh functionality</li>
                </ul>
                <span class="view-btn">View Mockup</span>
            </a>
            
            <a href="05_insights_dashboard.html" class="mockup-card">
                <span class="mockup-icon">📊</span>
                <div class="mockup-title">Insights Dashboard</div>
                <div class="mockup-description">
                    Analytics and insights dashboard with charts, achievements, and personal statistics.
                </div>
                <ul class="mockup-features">
                    <li>Summary statistics cards</li>
                    <li>Interactive charts</li>
                    <li>Achievement system</li>
                    <li>Personal insights</li>
                    <li>Progress tracking</li>
                </ul>
                <span class="view-btn">View Mockup</span>
            </a>
        </div>
        
        <!-- Features Section -->
        <div class="features-section">
            <div class="features-title">New Design Features</div>
            <div class="features-grid">
                <div class="feature-item">
                    <span class="feature-icon">🎨</span>
                    <div class="feature-title">Modern Design</div>
                    <div class="feature-description">
                        Beautiful gradients, glassmorphism effects, and modern card-based layouts
                    </div>
                </div>
                
                <div class="feature-item">
                    <span class="feature-icon">📱</span>
                    <div class="feature-title">Mobile-First</div>
                    <div class="feature-description">
                        Optimized for mobile devices with touch-friendly interactions and gestures
                    </div>
                </div>
                
                <div class="feature-item">
                    <span class="feature-icon">🧠</span>
                    <div class="feature-title">AI Integration</div>
                    <div class="feature-description">
                        Prominent AI features with beautiful information cards and insights
                    </div>
                </div>
                
                <div class="feature-item">
                    <span class="feature-icon">⚡</span>
                    <div class="feature-title">Performance</div>
                    <div class="feature-description">
                        Smooth animations, fast loading, and optimized user experience
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="footer-text">
                These mockups showcase the new beautiful UI design for IsitLocal? app.<br>
                Ready to implement in SwiftUI with modern iOS design patterns.
            </div>
        </div>
    </div>
</body>
</html>
