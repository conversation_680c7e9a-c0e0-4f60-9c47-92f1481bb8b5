<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IsitLocal? - AI Enhanced Info</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 414px;
            margin: 0 auto;
            min-height: 100vh;
            background: white;
        }
        
        /* Header */
        .header {
            position: relative;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 60px 20px 20px;
        }
        
        .back-btn {
            position: absolute;
            top: 60px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 12px;
            width: 40px;
            height: 40px;
            color: white;
            font-size: 18px;
            cursor: pointer;
        }
        
        .header-content {
            text-align: center;
        }
        
        .ai-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
        
        .header-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
        }
        
        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }
        
        /* Content */
        .content {
            padding: 30px 20px;
        }
        
        .product-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #f8f9ff, #e8f0ff);
            border-radius: 16px;
        }
        
        .product-title {
            font-size: 22px;
            font-weight: 700;
            color: #2d3436;
            margin-bottom: 8px;
        }
        
        .product-manufacturer {
            font-size: 16px;
            color: #636e72;
            margin-bottom: 12px;
        }
        
        .confidence-badge {
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
        }
        
        /* AI Insights Cards */
        .insights-container {
            display: grid;
            gap: 20px;
        }
        
        .insight-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
        }
        
        .insight-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
        }
        
        .insight-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }
        
        .description-icon { background: linear-gradient(135deg, #667eea, #764ba2); }
        .nutrition-icon { background: linear-gradient(135deg, #fd79a8, #e84393); }
        .sustainability-icon { background: linear-gradient(135deg, #00b894, #00a085); }
        .alternatives-icon { background: linear-gradient(135deg, #fdcb6e, #e17055); }
        .health-icon { background: linear-gradient(135deg, #74b9ff, #0984e3); }
        .validation-icon { background: linear-gradient(135deg, #a29bfe, #6c5ce7); }
        
        .insight-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3436;
        }
        
        .insight-content {
            color: #636e72;
            font-size: 15px;
            line-height: 1.6;
        }
        
        /* Validation Card Special Styling */
        .validation-card {
            border: 2px solid #00b894;
            background: linear-gradient(135deg, #f8fff8, #e8f5e8);
        }
        
        .validation-status {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
        }
        
        .status-icon {
            font-size: 20px;
        }
        
        .status-text {
            font-weight: 600;
            color: #00b894;
        }
        
        .confidence-score {
            background: rgba(0, 184, 148, 0.1);
            color: #00b894;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            margin-left: auto;
        }
        
        /* Local Alternatives Special Styling */
        .alternatives-list {
            margin-top: 15px;
        }
        
        .alternative-item {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 8px;
            border-left: 4px solid #fdcb6e;
        }
        
        .alternative-title {
            font-weight: 600;
            color: #2d3436;
            margin-bottom: 4px;
        }
        
        .alternative-description {
            font-size: 14px;
            color: #636e72;
        }
        
        /* Loading State */
        .loading-card {
            text-align: center;
            padding: 40px 20px;
            background: linear-gradient(45deg, #f8f9ff, #e8f0ff);
            border-radius: 16px;
            margin-bottom: 20px;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e9ecef;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            color: #636e72;
            font-size: 16px;
        }
        
        /* Refresh Button */
        .refresh-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 20px;
            transition: all 0.3s ease;
        }
        
        .refresh-btn:active {
            transform: translateY(1px);
        }
        
        .refresh-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <button class="back-btn">←</button>
            <div class="header-content">
                <div class="ai-icon">🧠</div>
                <div class="header-title">AI Enhanced Info</div>
                <div class="header-subtitle">Powered by Gemini AI</div>
            </div>
        </div>
        
        <!-- Content -->
        <div class="content">
            <!-- Product Header -->
            <div class="product-header">
                <div class="product-title">Organic Maple Syrup</div>
                <div class="product-manufacturer">by Canadian Maple Co.</div>
                <div class="confidence-badge">AI Confidence: 9/10</div>
            </div>
            
            <!-- AI Insights -->
            <div class="insights-container">
                <!-- Description -->
                <div class="insight-card">
                    <div class="insight-header">
                        <div class="insight-icon description-icon">📝</div>
                        <div class="insight-title">Enhanced Description</div>
                    </div>
                    <div class="insight-content">
                        This premium organic maple syrup is harvested from century-old sugar maple trees in Quebec's pristine forests. The traditional tapping and boiling process preserves the natural minerals and complex flavor profile, making it a superior choice for both culinary and nutritional purposes.
                    </div>
                </div>
                
                <!-- Nutrition -->
                <div class="insight-card">
                    <div class="insight-header">
                        <div class="insight-icon nutrition-icon">❤️</div>
                        <div class="insight-title">Nutrition Highlights</div>
                    </div>
                    <div class="insight-content">
                        Rich in antioxidants and minerals including manganese, zinc, and potassium. Contains fewer calories than honey and provides natural energy without artificial additives. Grade A Dark Robust offers the highest antioxidant content.
                    </div>
                </div>
                
                <!-- Sustainability -->
                <div class="insight-card">
                    <div class="insight-header">
                        <div class="insight-icon sustainability-icon">🌱</div>
                        <div class="insight-title">Sustainability Impact</div>
                    </div>
                    <div class="insight-content">
                        Sustainable forest management practices support biodiversity. Local production reduces transportation emissions by 75% compared to imported alternatives. The maple forest acts as a carbon sink, contributing to climate change mitigation.
                    </div>
                </div>
                
                <!-- Data Validation -->
                <div class="insight-card validation-card">
                    <div class="insight-header">
                        <div class="insight-icon validation-icon">✅</div>
                        <div class="insight-title">Data Validation</div>
                        <div class="confidence-score">Confidence: 9/10</div>
                    </div>
                    <div class="validation-status">
                        <span class="status-icon">✅</span>
                        <span class="status-text">Data appears valid</span>
                    </div>
                    <div class="insight-content">
                        Product information is consistent with Canadian food regulations. Barcode matches manufacturer database. Origin verification confirmed through supply chain analysis.
                    </div>
                </div>
                
                <!-- Local Alternatives -->
                <div class="insight-card">
                    <div class="insight-header">
                        <div class="insight-icon alternatives-icon">🏪</div>
                        <div class="insight-title">Local Alternatives</div>
                    </div>
                    <div class="insight-content">
                        <div class="alternatives-list">
                            <div class="alternative-item">
                                <div class="alternative-title">Sugar Bush Maple Farm</div>
                                <div class="alternative-description">Family-owned farm 15km away, offers farm tours and direct sales</div>
                            </div>
                            <div class="alternative-item">
                                <div class="alternative-title">Farmers Market - Saturday</div>
                                <div class="alternative-description">3 local maple producers sell fresh syrup every weekend</div>
                            </div>
                            <div class="alternative-item">
                                <div class="alternative-title">Co-op Natural Foods</div>
                                <div class="alternative-description">Stocks 5 different local maple syrup brands year-round</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Health Considerations -->
                <div class="insight-card">
                    <div class="insight-header">
                        <div class="insight-icon health-icon">🏥</div>
                        <div class="insight-title">Health Considerations</div>
                    </div>
                    <div class="insight-content">
                        Suitable for most dietary restrictions including vegan and gluten-free diets. Diabetics should consume in moderation due to natural sugar content. Contains trace amounts of beneficial compounds like quebecol, unique to maple syrup.
                    </div>
                </div>
            </div>
            
            <!-- Refresh Button -->
            <button class="refresh-btn">
                🔄 Refresh AI Analysis
            </button>
        </div>
    </div>
</body>
</html>
