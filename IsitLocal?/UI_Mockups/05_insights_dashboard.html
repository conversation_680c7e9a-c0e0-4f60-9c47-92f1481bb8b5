<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IsitLocal? - Insights Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 414px;
            margin: 0 auto;
            min-height: 100vh;
            background: white;
        }
        
        /* Header */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 20px 30px;
            text-align: center;
        }
        
        .header-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }
        
        /* Summary Cards */
        .summary-section {
            padding: 20px;
            background: white;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: linear-gradient(135deg, #f8f9ff, #e8f0ff);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        
        .summary-number {
            font-size: 32px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 8px;
        }
        
        .summary-label {
            font-size: 14px;
            color: #636e72;
            font-weight: 500;
        }
        
        .summary-change {
            font-size: 12px;
            margin-top: 4px;
            font-weight: 600;
        }
        
        .positive { color: #00b894; }
        .negative { color: #e17055; }
        
        /* Chart Section */
        .chart-section {
            padding: 0 20px 20px;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2d3436;
        }
        
        .chart-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
        }
        
        .chart-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3436;
        }
        
        .chart-period {
            font-size: 14px;
            color: #636e72;
            background: #f8f9fa;
            padding: 4px 12px;
            border-radius: 12px;
        }
        
        /* Mock Chart */
        .mock-chart {
            height: 200px;
            background: linear-gradient(45deg, #f8f9ff, #e8f0ff);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
        }
        
        .chart-bars {
            display: flex;
            align-items: end;
            gap: 8px;
            height: 120px;
        }
        
        .chart-bar {
            width: 20px;
            background: linear-gradient(to top, #667eea, #764ba2);
            border-radius: 4px 4px 0 0;
            opacity: 0.8;
        }
        
        .bar-1 { height: 60%; }
        .bar-2 { height: 80%; }
        .bar-3 { height: 45%; }
        .bar-4 { height: 90%; }
        .bar-5 { height: 70%; }
        .bar-6 { height: 100%; }
        .bar-7 { height: 55%; }
        
        .chart-legend {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #636e72;
        }
        
        /* Insights List */
        .insights-list {
            padding: 0 20px 20px;
        }
        
        .insight-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .insight-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
            flex-shrink: 0;
        }
        
        .local-icon { background: linear-gradient(135deg, #00b894, #00a085); }
        .sustainability-icon { background: linear-gradient(135deg, #fdcb6e, #e17055); }
        .health-icon { background: linear-gradient(135deg, #fd79a8, #e84393); }
        .savings-icon { background: linear-gradient(135deg, #74b9ff, #0984e3); }
        
        .insight-content {
            flex: 1;
        }
        
        .insight-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3436;
            margin-bottom: 4px;
        }
        
        .insight-description {
            font-size: 14px;
            color: #636e72;
            line-height: 1.4;
        }
        
        /* Achievements Section */
        .achievements-section {
            padding: 0 20px 20px;
        }
        
        .achievements-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }
        
        .achievement-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
        }
        
        .achievement-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }
        
        .achievement-title {
            font-size: 14px;
            font-weight: 600;
            color: #2d3436;
            margin-bottom: 4px;
        }
        
        .achievement-description {
            font-size: 12px;
            color: #636e72;
        }
        
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 12px 20px 30px;
            border-radius: 24px 24px 0 0;
        }
        
        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px;
            border-radius: 12px;
            transition: all 0.3s ease;
            color: #666;
            text-decoration: none;
        }
        
        .nav-item.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: translateY(-2px);
        }
        
        .nav-icon {
            font-size: 20px;
        }
        
        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-title">Your Insights</div>
            <div class="header-subtitle">Track your local shopping impact</div>
        </div>
        
        <!-- Summary Section -->
        <div class="summary-section">
            <div class="summary-grid">
                <div class="summary-card">
                    <div class="summary-number">47</div>
                    <div class="summary-label">Products Scanned</div>
                    <div class="summary-change positive">+12 this week</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number">68%</div>
                    <div class="summary-label">Local Products</div>
                    <div class="summary-change positive">+5% vs last month</div>
                </div>
            </div>
        </div>
        
        <!-- Chart Section -->
        <div class="chart-section">
            <div class="section-title">Weekly Scanning Activity</div>
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">Scans per Day</div>
                    <div class="chart-period">Last 7 days</div>
                </div>
                <div class="mock-chart">
                    <div class="chart-bars">
                        <div class="chart-bar bar-1"></div>
                        <div class="chart-bar bar-2"></div>
                        <div class="chart-bar bar-3"></div>
                        <div class="chart-bar bar-4"></div>
                        <div class="chart-bar bar-5"></div>
                        <div class="chart-bar bar-6"></div>
                        <div class="chart-bar bar-7"></div>
                    </div>
                </div>
                <div class="chart-legend">
                    <span>Mon</span>
                    <span>Tue</span>
                    <span>Wed</span>
                    <span>Thu</span>
                    <span>Fri</span>
                    <span>Sat</span>
                    <span>Sun</span>
                </div>
            </div>
        </div>
        
        <!-- Insights List -->
        <div class="insights-list">
            <div class="section-title">Key Insights</div>
            
            <div class="insight-item">
                <div class="insight-icon local-icon">🏪</div>
                <div class="insight-content">
                    <div class="insight-title">Local Champion</div>
                    <div class="insight-description">You've chosen local products 68% of the time this month!</div>
                </div>
            </div>
            
            <div class="insight-item">
                <div class="insight-icon sustainability-icon">🌱</div>
                <div class="insight-content">
                    <div class="insight-title">Carbon Footprint</div>
                    <div class="insight-description">Your choices saved an estimated 12kg of CO2 this month</div>
                </div>
            </div>
            
            <div class="insight-item">
                <div class="insight-icon health-icon">❤️</div>
                <div class="insight-content">
                    <div class="insight-title">Health Focus</div>
                    <div class="insight-description">85% of your scanned products are in healthy categories</div>
                </div>
            </div>
            
            <div class="insight-item">
                <div class="insight-icon savings-icon">💰</div>
                <div class="insight-content">
                    <div class="insight-title">Smart Shopping</div>
                    <div class="insight-description">You've saved approximately $45 by choosing local alternatives</div>
                </div>
            </div>
        </div>
        
        <!-- Achievements Section -->
        <div class="achievements-section">
            <div class="section-title">Recent Achievements</div>
            <div class="achievements-grid">
                <div class="achievement-card">
                    <div class="achievement-icon">🏆</div>
                    <div class="achievement-title">Local Hero</div>
                    <div class="achievement-description">50+ local products scanned</div>
                </div>
                <div class="achievement-card">
                    <div class="achievement-icon">🌟</div>
                    <div class="achievement-title">Streak Master</div>
                    <div class="achievement-description">7 days scanning streak</div>
                </div>
                <div class="achievement-card">
                    <div class="achievement-icon">🌍</div>
                    <div class="achievement-title">Eco Warrior</div>
                    <div class="achievement-description">10kg CO2 saved</div>
                </div>
                <div class="achievement-card">
                    <div class="achievement-icon">📊</div>
                    <div class="achievement-title">Data Explorer</div>
                    <div class="achievement-description">Used AI insights 20+ times</div>
                </div>
            </div>
        </div>
        
        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <div class="nav-items">
                <a href="#" class="nav-item">
                    <div class="nav-icon">📱</div>
                    <div class="nav-label">Scan</div>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">📷</div>
                    <div class="nav-label">Photo</div>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">⭐</div>
                    <div class="nav-label">Favorites</div>
                </a>
                <a href="#" class="nav-item active">
                    <div class="nav-icon">📊</div>
                    <div class="nav-label">Insights</div>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">⚙️</div>
                    <div class="nav-label">Settings</div>
                </a>
            </div>
        </div>
    </div>
</body>
</html>
