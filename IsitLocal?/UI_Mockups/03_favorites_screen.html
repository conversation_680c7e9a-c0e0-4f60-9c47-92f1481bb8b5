<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IsitLocal? - Favorites</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 414px;
            margin: 0 auto;
            min-height: 100vh;
            background: white;
        }
        
        /* Header */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 20px 30px;
            text-align: center;
        }
        
        .header-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .favorites-count {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 8px 16px;
            margin-top: 15px;
            display: inline-block;
            font-size: 14px;
            font-weight: 600;
        }
        
        /* Search Bar */
        .search-container {
            padding: 20px;
            background: white;
            border-bottom: 1px solid #e9ecef;
        }
        
        .search-bar {
            position: relative;
        }
        
        .search-input {
            width: 100%;
            padding: 16px 50px 16px 20px;
            border: 2px solid #e9ecef;
            border-radius: 16px;
            font-size: 16px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
        }
        
        .search-icon {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #636e72;
            font-size: 18px;
        }
        
        /* Filter Tabs */
        .filter-tabs {
            display: flex;
            padding: 0 20px 20px;
            gap: 10px;
            background: white;
            border-bottom: 1px solid #e9ecef;
        }
        
        .filter-tab {
            padding: 8px 16px;
            border: 2px solid #e9ecef;
            border-radius: 20px;
            background: white;
            color: #636e72;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .filter-tab.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
        }
        
        /* Favorites List */
        .favorites-list {
            padding: 20px;
        }
        
        .favorite-item {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .favorite-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        .item-header {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .item-image {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            background: linear-gradient(45deg, #f8f9ff, #e8f0ff);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            flex-shrink: 0;
        }
        
        .item-info {
            flex: 1;
        }
        
        .item-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3436;
            margin-bottom: 4px;
        }
        
        .item-manufacturer {
            font-size: 14px;
            color: #636e72;
            margin-bottom: 8px;
        }
        
        .item-badges {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .badge {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .origin-badge {
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
        }
        
        .category-badge {
            background: #e9ecef;
            color: #636e72;
        }
        
        .item-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .item-date {
            font-size: 12px;
            color: #b2bec3;
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            width: 36px;
            height: 36px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .share-btn {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .delete-btn {
            background: #ffebee;
            color: #d32f2f;
        }
        
        .notes-btn {
            background: #f3e5f5;
            color: #7b1fa2;
        }
        
        .action-btn:hover {
            transform: scale(1.1);
        }
        
        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #636e72;
        }
        
        .empty-icon {
            font-size: 80px;
            margin-bottom: 20px;
            opacity: 0.3;
        }
        
        .empty-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .empty-subtitle {
            font-size: 16px;
            line-height: 1.5;
        }
        
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 12px 20px 30px;
            border-radius: 24px 24px 0 0;
        }
        
        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px;
            border-radius: 12px;
            transition: all 0.3s ease;
            color: #666;
            text-decoration: none;
        }
        
        .nav-item.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: translateY(-2px);
        }
        
        .nav-icon {
            font-size: 20px;
        }
        
        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-title">My Favorites</div>
            <div class="header-subtitle">Your saved local products</div>
            <div class="favorites-count">12 favorites</div>
        </div>
        
        <!-- Search Bar -->
        <div class="search-container">
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="Search your favorites...">
                <div class="search-icon">🔍</div>
            </div>
        </div>
        
        <!-- Filter Tabs -->
        <div class="filter-tabs">
            <div class="filter-tab active">All</div>
            <div class="filter-tab">Local</div>
            <div class="filter-tab">Food</div>
            <div class="filter-tab">Recent</div>
        </div>
        
        <!-- Favorites List -->
        <div class="favorites-list">
            <div class="favorite-item">
                <div class="item-header">
                    <div class="item-image">🍯</div>
                    <div class="item-info">
                        <div class="item-title">Organic Maple Syrup</div>
                        <div class="item-manufacturer">Canadian Maple Co.</div>
                        <div class="item-badges">
                            <div class="badge origin-badge">🇨🇦 Canada</div>
                            <div class="badge category-badge">Food</div>
                        </div>
                    </div>
                </div>
                <div class="item-actions">
                    <div class="item-date">Added 2 days ago</div>
                    <div class="action-buttons">
                        <button class="action-btn share-btn">📤</button>
                        <button class="action-btn notes-btn">📝</button>
                        <button class="action-btn delete-btn">🗑️</button>
                    </div>
                </div>
            </div>
            
            <div class="favorite-item">
                <div class="item-header">
                    <div class="item-image">🧀</div>
                    <div class="item-info">
                        <div class="item-title">Artisan Cheddar Cheese</div>
                        <div class="item-manufacturer">Local Dairy Farm</div>
                        <div class="item-badges">
                            <div class="badge origin-badge">🇨🇦 Canada</div>
                            <div class="badge category-badge">Dairy</div>
                        </div>
                    </div>
                </div>
                <div class="item-actions">
                    <div class="item-date">Added 1 week ago</div>
                    <div class="action-buttons">
                        <button class="action-btn share-btn">📤</button>
                        <button class="action-btn notes-btn">📝</button>
                        <button class="action-btn delete-btn">🗑️</button>
                    </div>
                </div>
            </div>
            
            <div class="favorite-item">
                <div class="item-header">
                    <div class="item-image">🍞</div>
                    <div class="item-info">
                        <div class="item-title">Sourdough Bread</div>
                        <div class="item-manufacturer">Village Bakery</div>
                        <div class="item-badges">
                            <div class="badge origin-badge">🇨🇦 Canada</div>
                            <div class="badge category-badge">Bakery</div>
                        </div>
                    </div>
                </div>
                <div class="item-actions">
                    <div class="item-date">Added 2 weeks ago</div>
                    <div class="action-buttons">
                        <button class="action-btn share-btn">📤</button>
                        <button class="action-btn notes-btn">📝</button>
                        <button class="action-btn delete-btn">🗑️</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <div class="nav-items">
                <a href="#" class="nav-item">
                    <div class="nav-icon">📱</div>
                    <div class="nav-label">Scan</div>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">📷</div>
                    <div class="nav-label">Photo</div>
                </a>
                <a href="#" class="nav-item active">
                    <div class="nav-icon">⭐</div>
                    <div class="nav-label">Favorites</div>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">📊</div>
                    <div class="nav-label">Insights</div>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">⚙️</div>
                    <div class="nav-label">Settings</div>
                </a>
            </div>
        </div>
    </div>
</body>
</html>
