<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IsitLocal? - Product Detail</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 414px;
            margin: 0 auto;
            min-height: 100vh;
            background: white;
        }
        
        /* Header */
        .header {
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 20px 20px;
        }
        
        .back-btn {
            position: absolute;
            top: 60px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 12px;
            width: 40px;
            height: 40px;
            color: white;
            font-size: 18px;
            cursor: pointer;
        }
        
        .header-title {
            font-size: 24px;
            font-weight: 700;
            text-align: center;
        }
        
        /* Product Image */
        .product-image {
            position: relative;
            height: 300px;
            background: linear-gradient(45deg, #f8f9ff, #e8f0ff);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: -20px 20px 0;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .product-img {
            width: 200px;
            height: 200px;
            object-fit: cover;
            border-radius: 16px;
        }
        
        .placeholder-icon {
            font-size: 80px;
            opacity: 0.3;
        }
        
        .origin-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(0, 184, 148, 0.3);
        }
        
        /* Content */
        .content {
            padding: 30px 20px;
        }
        
        .product-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            color: #2d3436;
        }
        
        .product-manufacturer {
            font-size: 16px;
            color: #636e72;
            margin-bottom: 20px;
        }
        
        .barcode-info {
            background: #f8f9fa;
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .barcode-icon {
            font-size: 20px;
        }
        
        .barcode-text {
            font-family: 'Courier New', monospace;
            font-weight: 600;
            color: #2d3436;
        }
        
        /* Feature Cards */
        .feature-cards {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        .feature-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }
        
        .feature-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }
        
        .ai-icon { background: linear-gradient(135deg, #ff6b6b, #ee5a24); }
        .nutrition-icon { background: linear-gradient(135deg, #fd79a8, #e84393); }
        .sustainability-icon { background: linear-gradient(135deg, #00b894, #00a085); }
        .compare-icon { background: linear-gradient(135deg, #0984e3, #74b9ff); }
        
        .feature-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3436;
        }
        
        .feature-subtitle {
            font-size: 14px;
            color: #636e72;
        }
        
        .feature-description {
            color: #636e72;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .chevron {
            margin-left: auto;
            color: #b2bec3;
            font-size: 16px;
        }
        
        /* Info Sections */
        .info-section {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2d3436;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 500;
            color: #636e72;
        }
        
        .info-value {
            font-weight: 600;
            color: #2d3436;
            text-align: right;
            flex: 1;
            margin-left: 20px;
        }
        
        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 12px;
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
            position: sticky;
            bottom: 0;
        }
        
        .action-btn {
            flex: 1;
            padding: 16px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .primary-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .secondary-btn {
            background: #f8f9fa;
            color: #667eea;
            border: 2px solid #e9ecef;
        }
        
        .action-btn:active {
            transform: translateY(1px);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <button class="back-btn">←</button>
            <div class="header-title">Product Details</div>
        </div>
        
        <!-- Product Image -->
        <div class="product-image">
            <div class="placeholder-icon">📦</div>
            <div class="origin-badge">🇨🇦 Canada</div>
        </div>
        
        <!-- Content -->
        <div class="content">
            <h1 class="product-title">Organic Maple Syrup</h1>
            <p class="product-manufacturer">by Canadian Maple Co.</p>
            
            <div class="barcode-info">
                <span class="barcode-icon">📊</span>
                <span class="barcode-text">123456789012</span>
            </div>
            
            <!-- Feature Cards -->
            <div class="feature-cards">
                <div class="feature-card">
                    <div class="feature-header">
                        <div class="feature-icon ai-icon">🧠</div>
                        <div>
                            <div class="feature-title">AI Enhanced Info</div>
                            <div class="feature-subtitle">Powered by Gemini AI</div>
                        </div>
                        <div class="chevron">›</div>
                    </div>
                    <div class="feature-description">
                        Get detailed insights, local alternatives, and enhanced product information using AI analysis.
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-header">
                        <div class="feature-icon nutrition-icon">❤️</div>
                        <div>
                            <div class="feature-title">Nutrition Information</div>
                            <div class="feature-subtitle">Health insights</div>
                        </div>
                        <div class="chevron">›</div>
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-header">
                        <div class="feature-icon sustainability-icon">🌱</div>
                        <div>
                            <div class="feature-title">Sustainability Score</div>
                            <div class="feature-subtitle">Environmental impact</div>
                        </div>
                        <div class="chevron">›</div>
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-header">
                        <div class="feature-icon compare-icon">⚖️</div>
                        <div>
                            <div class="feature-title">Compare Products</div>
                            <div class="feature-subtitle">Find alternatives</div>
                        </div>
                        <div class="chevron">›</div>
                    </div>
                </div>
            </div>
            
            <!-- Product Information -->
            <div class="info-section">
                <div class="section-title">
                    <span>📋</span>
                    Product Information
                </div>
                <div class="info-row">
                    <span class="info-label">Category</span>
                    <span class="info-value">Food & Beverages</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Origin</span>
                    <span class="info-value">🇨🇦 Canada</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Manufacturer</span>
                    <span class="info-value">Canadian Maple Co.</span>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="action-btn secondary-btn">⭐ Favorite</button>
            <button class="action-btn primary-btn">📤 Share</button>
        </div>
    </div>
</body>
</html>
