//
//  SecuritySettingsView.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-08.
//

import SwiftUI
import LocalAuthentication

struct SecuritySettingsView: View {
    @State private var isSecurityEnabled = SecurityManager.shared.isSecurityEnabled
    @State private var isBiometricEnabled = SecurityManager.shared.isBiometricEnabled
    @State private var biometricType: BiometricType = .none
    @State private var showingAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""

    var body: some View {
        Form {
            Section(header: Text("App Security")) {
                Toggle("Enable Security Features", isOn: $isSecurityEnabled)
                    .onChange(of: isSecurityEnabled) {
                        SecurityManager.shared.setSecurityEnabled($1)

                        if !$1 {
                            // If security is disabled, also disable biometrics
                            isBiometricEnabled = false
                            SecurityManager.shared.setBiometricEnabled(false)
                        }
                    }

                if isSecurityEnabled {
                    Toggle(biometricToggleText, isOn: $isBiometricEnabled)
                        .onChange(of: isBiometricEnabled) {
                            if $1 {
                                // Verify biometric availability before enabling
                                let biometricStatus = SecurityManager.shared.isBiometricAvailable()
                                if biometricStatus.available {
                                    SecurityManager.shared.setBiometricEnabled(true)
                                } else {
                                    // Show alert if biometrics are not available
                                    isBiometricEnabled = false
                                    alertTitle = "Biometrics Not Available"
                                    alertMessage = biometricStatus.error?.localizedDescription ?? "Your device doesn't support biometric authentication."
                                    showingAlert = true
                                }
                            } else {
                                SecurityManager.shared.setBiometricEnabled(false)
                            }
                        }
                        .disabled(!biometricsAvailable)
                }
            }

            Section(header: Text("Data Protection"), footer: Text("Enabling data protection encrypts your scan history and saved products.")) {
                Toggle("Encrypt Saved Data", isOn: .constant(isSecurityEnabled))
                    .disabled(true) // This is tied to the main security toggle
            }

            Section(header: Text("Privacy")) {
                Toggle("Anonymous Usage Analytics", isOn: .constant(false))
                    .disabled(!isSecurityEnabled)

                Toggle("Share Location Data", isOn: .constant(true))
                    .disabled(!isSecurityEnabled)
            }

            if isSecurityEnabled {
                Section(header: Text("Security Information")) {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Security Status: Active")
                            .font(.headline)
                            .foregroundColor(.green)

                        Text("Your data is protected with industry-standard encryption. Biometric authentication adds an extra layer of security to prevent unauthorized access.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 5)
                }
            }
        }
        .navigationTitle("Security Settings")
        .onAppear {
            // Check biometric availability
            let biometricStatus = SecurityManager.shared.isBiometricAvailable()
            biometricType = biometricStatus.biometricType
        }
        .alert(isPresented: $showingAlert) {
            Alert(
                title: Text(alertTitle),
                message: Text(alertMessage),
                dismissButton: .default(Text("OK"))
            )
        }
    }

    // Helper computed properties

    private var biometricsAvailable: Bool {
        let biometricStatus = SecurityManager.shared.isBiometricAvailable()
        return biometricStatus.available
    }

    private var biometricToggleText: String {
        switch biometricType {
        case .faceID:
            return "Use Face ID"
        case .touchID:
            return "Use Touch ID"
        case .none:
            return "Use Biometric Authentication"
        }
    }
}
