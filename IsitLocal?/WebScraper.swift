//
//  WebScraper.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-02.
//

import Foundation
import SwiftUI

class WebScraper {
    
    // Function to scrape product data from various websites
    static func scrapeProductData(barcode: String) async throws -> (String?, String?, String?, String?, String?) {
        // Try multiple sources in sequence
        do {
            // Try UPC Database
            let upcResult = try await scrapeUPCDatabase(barcode: barcode)
            if upcResult.0 != nil {
                print("Successfully scraped UPC Database for barcode: \(barcode)")
                return upcResult
            }
        } catch {
            print("Error scraping UPC Database: \(error.localizedDescription)")
        }
        
        do {
            // Try Barcode Spider
            let spiderResult = try await scrapeBarcodeSpider(barcode: barcode)
            if spiderResult.0 != nil {
                print("Successfully scraped Barcode Spider for barcode: \(barcode)")
                return spiderResult
            }
        } catch {
            print("Error scraping Barcode Spider: \(error.localizedDescription)")
        }
        
        do {
            // Try Google Product Search
            let googleResult = try await scrapeGoogleProduct(barcode: barcode)
            if googleResult.0 != nil {
                print("Successfully scraped Google Product Search for barcode: \(barcode)")
                return googleResult
            }
        } catch {
            print("Error scraping Google Product Search: \(error.localizedDescription)")
        }
        
        // If all scraping attempts fail, throw an error
        print("No product data found for barcode: \(barcode)")
        throw NSError(domain: "WebScraper", code: 404, userInfo: [NSLocalizedDescriptionKey: "No product data found"])
    }
    
    // Scrape UPC Database
    private static func scrapeUPCDatabase(barcode: String) async throws -> (String?, String?, String?, String?, String?) {
        let urlString = "https://www.upcdatabase.com/item/\(barcode)"
        guard let url = URL(string: urlString) else {
            throw URLError(.badURL)
        }
        
        let (data, _) = try await URLSession.shared.data(from: url)
        guard let htmlString = String(data: data, encoding: .utf8) else {
            throw NSError(domain: "WebScraper", code: 500, userInfo: [NSLocalizedDescriptionKey: "Failed to decode HTML"])
        }
        
        // Extract product name
        var productName: String? = nil
        if let nameRange = htmlString.range(of: "<td>Description</td><td>") {
            let afterNameStart = htmlString[nameRange.upperBound...]
            if let nameEndRange = afterNameStart.range(of: "</td>") {
                productName = String(afterNameStart[..<nameEndRange.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }
        
        // Extract manufacturer
        var manufacturer: String? = nil
        if let mfgRange = htmlString.range(of: "<td>Manufacturer</td><td>") {
            let afterMfgStart = htmlString[mfgRange.upperBound...]
            if let mfgEndRange = afterMfgStart.range(of: "</td>") {
                manufacturer = String(afterMfgStart[..<mfgEndRange.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }
        
        // Extract category
        var category: String? = nil
        if let catRange = htmlString.range(of: "<td>Category</td><td>") {
            let afterCatStart = htmlString[catRange.upperBound...]
            if let catEndRange = afterCatStart.range(of: "</td>") {
                category = String(afterCatStart[..<catEndRange.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }
        
        // Extract image URL
        var imageUrl: String? = nil
        if let imgRange = htmlString.range(of: "<img src=\"") {
            let afterImgStart = htmlString[imgRange.upperBound...]
            if let imgEndRange = afterImgStart.range(of: "\"") {
                imageUrl = String(afterImgStart[..<imgEndRange.lowerBound])
                if !imageUrl!.hasPrefix("http") {
                    imageUrl = "https://www.upcdatabase.com" + imageUrl!
                }
            }
        }
        
        return (productName, manufacturer, "Ingredients not available from UPC Database", category, imageUrl)
    }
    
    // Scrape Barcode Spider
    private static func scrapeBarcodeSpider(barcode: String) async throws -> (String?, String?, String?, String?, String?) {
        let urlString = "https://www.barcodespider.com/\(barcode)"
        guard let url = URL(string: urlString) else {
            throw URLError(.badURL)
        }
        
        let (data, _) = try await URLSession.shared.data(from: url)
        guard let htmlString = String(data: data, encoding: .utf8) else {
            throw NSError(domain: "WebScraper", code: 500, userInfo: [NSLocalizedDescriptionKey: "Failed to decode HTML"])
        }
        
        // Extract product name
        var productName: String? = nil
        if let nameRange = htmlString.range(of: "<h1 class=\"product-name\">") {
            let afterNameStart = htmlString[nameRange.upperBound...]
            if let nameEndRange = afterNameStart.range(of: "</h1>") {
                productName = String(afterNameStart[..<nameEndRange.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }
        
        // Extract manufacturer
        var manufacturer: String? = nil
        if let mfgRange = htmlString.range(of: "<td>Brand</td><td>") {
            let afterMfgStart = htmlString[mfgRange.upperBound...]
            if let mfgEndRange = afterMfgStart.range(of: "</td>") {
                manufacturer = String(afterMfgStart[..<mfgEndRange.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }
        
        // Extract category
        var category: String? = nil
        if let catRange = htmlString.range(of: "<td>Category</td><td>") {
            let afterCatStart = htmlString[catRange.upperBound...]
            if let catEndRange = afterCatStart.range(of: "</td>") {
                category = String(afterCatStart[..<catEndRange.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }
        
        // Extract ingredients
        var ingredients: String? = nil
        if let ingRange = htmlString.range(of: "<td>Ingredients</td><td>") {
            let afterIngStart = htmlString[ingRange.upperBound...]
            if let ingEndRange = afterIngStart.range(of: "</td>") {
                ingredients = String(afterIngStart[..<ingEndRange.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }
        
        // Extract image URL
        var imageUrl: String? = nil
        if let imgRange = htmlString.range(of: "<img class=\"product-image\" src=\"") {
            let afterImgStart = htmlString[imgRange.upperBound...]
            if let imgEndRange = afterImgStart.range(of: "\"") {
                imageUrl = String(afterImgStart[..<imgEndRange.lowerBound])
            }
        }
        
        return (productName, manufacturer, ingredients, category, imageUrl)
    }
    
    // Scrape Google Product Search
    private static func scrapeGoogleProduct(barcode: String) async throws -> (String?, String?, String?, String?, String?) {
        let urlString = "https://www.google.com/search?q=\(barcode)+product"
        guard let url = URL(string: urlString) else {
            throw URLError(.badURL)
        }
        
        // Set up a request with a browser-like user agent
        var request = URLRequest(url: url)
        request.addValue("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15", forHTTPHeaderField: "User-Agent")
        
        let (data, _) = try await URLSession.shared.data(for: request)
        guard let htmlString = String(data: data, encoding: .utf8) else {
            throw NSError(domain: "WebScraper", code: 500, userInfo: [NSLocalizedDescriptionKey: "Failed to decode HTML"])
        }
        
        // Extract product name from search results
        var productName: String? = nil
        if let titleRange = htmlString.range(of: "<h3 class=\"LC20lb") {
            let afterTitleStart = htmlString[titleRange.upperBound...]
            if let closingTag = afterTitleStart.range(of: ">") {
                let titleContent = afterTitleStart[closingTag.upperBound...]
                if let titleEndRange = titleContent.range(of: "</h3>") {
                    productName = String(titleContent[..<titleEndRange.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
                }
            }
        }
        
        // Extract description snippet
        var description: String? = nil
        if let descRange = htmlString.range(of: "<span class=\"st\">") {
            let afterDescStart = htmlString[descRange.upperBound...]
            if let descEndRange = afterDescStart.range(of: "</span>") {
                description = String(afterDescStart[..<descEndRange.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }
        
        // Try to extract manufacturer from description
        var manufacturer: String? = nil
        if let desc = description, let byIndex = desc.range(of: " by ") {
            let afterBy = desc[byIndex.upperBound...]
            if let endOfMfg = afterBy.range(of: " ") {
                manufacturer = String(afterBy[..<endOfMfg.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
            } else {
                manufacturer = String(afterBy).trimmingCharacters(in: .whitespacesAndNewlines)
            }
        }
        
        // Extract image URL
        var imageUrl: String? = nil
        if let imgRange = htmlString.range(of: "<img src=\"") {
            let afterImgStart = htmlString[imgRange.upperBound...]
            if let imgEndRange = afterImgStart.range(of: "\"") {
                let potentialUrl = String(afterImgStart[..<imgEndRange.lowerBound])
                if potentialUrl.contains("http") && !potentialUrl.contains("google.com/favicon") {
                    imageUrl = potentialUrl
                }
            }
        }
        
        return (productName, manufacturer, description, "General", imageUrl)
    }
}
