//
//  GeminiAPIManager.swift
//  IsitLocal?
//
//  Created by Augment Agent on 2025-06-11.
//  IsitLocal AI - Enhanced product information, image recognition, and data validation
//  (Powered by Google's free Gemini API but branded as IsitLocal AI for competitive protection)
//

import Foundation
import UIKit

class GeminiAPIManager {
    static let shared = GeminiAPIManager()
    
    // API key loaded from Info.plist or UserDefaults
    private var apiKey: String = ""
    private let baseURL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent"
    
    private init() {
        // Debug: Print all Info.plist keys
        if let infoPlist = Bundle.main.infoDictionary {
            print("🔍 All Info.plist keys: \(infoPlist.keys)")
        }

        // Try to load API key from Info.plist first
        if let infoPlistKey = Bundle.main.infoDictionary?["GeminiAPIKey"] as? String {
            print("🔍 GeminiAPIManager - Raw API key from Info.plist: '\(infoPlistKey)'")
            print("🔍 Key length: \(infoPlistKey.count)")
            print("🔍 Key starts with AIza: \(infoPlistKey.hasPrefix("AIza"))")

            if !infoPlistKey.isEmpty && infoPlistKey.count > 10 && infoPlistKey.hasPrefix("AIza") {
                self.apiKey = infoPlistKey.trimmingCharacters(in: .whitespacesAndNewlines)
                print("✅ IsitLocal AI key loaded from Info.plist successfully!")
                print("✅ Final key: \(self.apiKey.prefix(10))...")
                return
            } else {
                print("❌ Invalid API key format in Info.plist")
            }
        } else {
            print("❌ GeminiAPIKey not found in Info.plist")
        }

        // Fallback to UserDefaults
        if let userDefaultsKey = UserDefaults.standard.string(forKey: "GeminiAPIKey"),
           !userDefaultsKey.isEmpty && userDefaultsKey.hasPrefix("AIza") {
            self.apiKey = userDefaultsKey
            print("✅ IsitLocal AI key loaded from UserDefaults")
        } else {
            print("❌ No valid IsitLocal AI key configured anywhere")
        }
    }
    
    // MARK: - API Key Configuration
    func setAPIKey(_ key: String) {
        self.apiKey = key
        print("Gemini API key configured")
    }

    func isAPIKeyConfigured() -> Bool {
        let isConfigured = !apiKey.isEmpty && apiKey != "YOUR_FREE_GEMINI_API_KEY_HERE" && apiKey.hasPrefix("AIza")
        print("🔍 GeminiAPIManager.isAPIKeyConfigured(): \(isConfigured)")
        print("🔍 API key length: \(apiKey.count)")
        print("🔍 API key prefix: \(apiKey.prefix(10))...")
        return isConfigured
    }

    // MARK: - Simple API Test
    func testAPIConnection() async -> Bool {
        guard !apiKey.isEmpty else {
            print("❌ No API key available for testing")
            return false
        }

        print("🧪 Testing API connection with key: \(apiKey.prefix(10))...")

        let testURL = "\(baseURL)?key=\(apiKey)"
        guard let url = URL(string: testURL) else {
            print("❌ Invalid URL: \(testURL)")
            return false
        }

        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        [
                            "text": "Say hello"
                        ]
                    ]
                ]
            ]
        ]

        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody)

            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.httpBody = jsonData

            let (data, response) = try await URLSession.shared.data(for: request)

            if let httpResponse = response as? HTTPURLResponse {
                print("🔍 API Response Status: \(httpResponse.statusCode)")

                if httpResponse.statusCode == 200 {
                    if let responseString = String(data: data, encoding: .utf8) {
                        print("✅ API Test Successful!")
                        print("📝 Response: \(responseString.prefix(200))...")
                        return true
                    }
                } else {
                    print("❌ API Error: \(httpResponse.statusCode)")
                    if let errorData = String(data: data, encoding: .utf8) {
                        print("Error details: \(errorData)")
                    }
                }
            }
        } catch {
            print("❌ API Test Failed: \(error.localizedDescription)")
        }

        return false
    }
    
    // MARK: - Enhanced Product Information
    func enhanceProductInformation(
        productName: String?,
        manufacturer: String?,
        barcode: String,
        category: String?,
        origin: String?
    ) async throws -> EnhancedProductInfo {
        
        let prompt = """
        Analyze this product and provide enhanced information:
        
        Product Name: \(productName ?? "Unknown")
        Manufacturer: \(manufacturer ?? "Unknown")
        Barcode: \(barcode)
        Category: \(category ?? "Unknown")
        Origin: \(origin ?? "Unknown")
        
        Please provide:
        1. Enhanced product description (2-3 sentences)
        2. Nutritional highlights (if food product)
        3. Sustainability information
        4. Alternative local products (if available)
        5. Health considerations
        6. Usage recommendations
        
        Format as JSON with keys: description, nutrition, sustainability, alternatives, health, usage
        Keep responses concise and factual.
        """
        
        return try await makeGeminiRequest(prompt: prompt, responseType: EnhancedProductInfo.self)
    }
    
    // MARK: - Image Recognition and Analysis
    func analyzeProductImage(_ image: UIImage, barcode: String?) async throws -> ImageAnalysisResult {
        
        // Convert image to base64
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            throw GeminiError.imageProcessingFailed
        }
        let base64Image = imageData.base64EncodedString()
        
        let prompt = """
        Analyze this product image and extract information:
        
        \(barcode != nil ? "Expected barcode: \(barcode!)" : "No barcode provided")
        
        Please identify:
        1. Product name and brand
        2. Product category
        3. Visible ingredients or materials
        4. Country of origin (if visible)
        5. Package size or quantity
        6. Any certifications or labels
        7. Confidence level (1-10)
        
        Format as JSON with keys: productName, brand, category, ingredients, origin, size, certifications, confidence
        """
        
        return try await makeGeminiRequestWithImage(
            prompt: prompt,
            base64Image: base64Image,
            responseType: ImageAnalysisResult.self
        )
    }
    
    // MARK: - Data Validation and Enhancement
    func validateAndEnhanceProductData(
        productName: String?,
        manufacturer: String?,
        barcode: String,
        origin: String?,
        category: String?
    ) async throws -> ValidationResult {
        
        let prompt = """
        Validate and enhance this product data:
        
        Product: \(productName ?? "Unknown")
        Manufacturer: \(manufacturer ?? "Unknown")
        Barcode: \(barcode)
        Origin: \(origin ?? "Unknown")
        Category: \(category ?? "Unknown")
        
        Please:
        1. Verify if the data seems consistent
        2. Suggest corrections if needed
        3. Provide confidence score (1-10)
        4. Add missing information if possible
        5. Flag any inconsistencies
        
        Format as JSON with keys: isValid, confidence, corrections, suggestions, inconsistencies
        """
        
        return try await makeGeminiRequest(prompt: prompt, responseType: ValidationResult.self)
    }
    
    // MARK: - Local Product Recommendations
    func findLocalAlternatives(
        productName: String,
        category: String?,
        userLocation: String = "Canada"
    ) async throws -> LocalAlternatives {
        
        let prompt = """
        Find local alternatives for this product in \(userLocation):
        
        Product: \(productName)
        Category: \(category ?? "Unknown")
        
        Please suggest:
        1. Local brands or manufacturers
        2. Similar products made locally
        3. Seasonal alternatives
        4. Environmental benefits of choosing local
        5. Where to find these alternatives
        
        Format as JSON with keys: localBrands, similarProducts, seasonal, benefits, whereToFind
        """
        
        return try await makeGeminiRequest(prompt: prompt, responseType: LocalAlternatives.self)
    }
    
    // MARK: - Private API Methods
    private func makeGeminiRequest<T: Codable>(
        prompt: String,
        responseType: T.Type
    ) async throws -> T {
        
        guard !apiKey.isEmpty && apiKey != "YOUR_FREE_GEMINI_API_KEY_HERE" else {
            throw GeminiError.apiKeyNotSet
        }
        
        guard let url = URL(string: "\(baseURL)?key=\(apiKey)") else {
            throw GeminiError.invalidURL
        }
        
        let requestBody = GeminiRequest(
            contents: [
                GeminiContent(
                    parts: [GeminiPart(text: prompt)]
                )
            ]
        )
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        do {
            request.httpBody = try JSONEncoder().encode(requestBody)
        } catch {
            throw GeminiError.encodingFailed
        }
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw GeminiError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            print("Gemini API Error: Status \(httpResponse.statusCode)")
            if let errorData = String(data: data, encoding: .utf8) {
                print("Error response: \(errorData)")
            }
            throw GeminiError.apiError(httpResponse.statusCode)
        }
        
        do {
            let geminiResponse = try JSONDecoder().decode(GeminiResponse.self, from: data)
            
            guard let content = geminiResponse.candidates.first?.content.parts.first?.text else {
                throw GeminiError.noContent
            }
            
            // Clean the content - remove markdown code blocks if present
            let cleanedContent = cleanJSONResponse(content)
            print("🔍 Cleaned response: \(cleanedContent.prefix(200))...")

            // Try to parse as JSON
            if let jsonData = cleanedContent.data(using: .utf8) {
                return try JSONDecoder().decode(responseType, from: jsonData)
            } else {
                throw GeminiError.invalidJSONResponse
            }
            
        } catch {
            print("Gemini parsing error: \(error)")
            throw GeminiError.decodingFailed
        }
    }
    
    private func makeGeminiRequestWithImage<T: Codable>(
        prompt: String,
        base64Image: String,
        responseType: T.Type
    ) async throws -> T {
        
        guard !apiKey.isEmpty && apiKey != "YOUR_FREE_GEMINI_API_KEY_HERE" else {
            throw GeminiError.apiKeyNotSet
        }
        
        guard let url = URL(string: "\(baseURL)?key=\(apiKey)") else {
            throw GeminiError.invalidURL
        }
        
        let requestBody = GeminiRequest(
            contents: [
                GeminiContent(
                    parts: [
                        GeminiPart(text: prompt),
                        GeminiPart(
                            inlineData: GeminiInlineData(
                                mimeType: "image/jpeg",
                                data: base64Image
                            )
                        )
                    ]
                )
            ]
        )
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        do {
            request.httpBody = try JSONEncoder().encode(requestBody)
        } catch {
            throw GeminiError.encodingFailed
        }
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw GeminiError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            print("Gemini API Error: Status \(httpResponse.statusCode)")
            throw GeminiError.apiError(httpResponse.statusCode)
        }
        
        do {
            let geminiResponse = try JSONDecoder().decode(GeminiResponse.self, from: data)
            
            guard let content = geminiResponse.candidates.first?.content.parts.first?.text else {
                throw GeminiError.noContent
            }
            
            // Clean the content - remove markdown code blocks if present
            let cleanedContent = cleanJSONResponse(content)
            print("🔍 Cleaned response: \(cleanedContent.prefix(200))...")

            // Try to parse as JSON
            if let jsonData = cleanedContent.data(using: .utf8) {
                return try JSONDecoder().decode(responseType, from: jsonData)
            } else {
                throw GeminiError.invalidJSONResponse
            }
            
        } catch {
            print("Gemini parsing error: \(error)")
            throw GeminiError.decodingFailed
        }
    }

    // MARK: - Helper Functions
    private func cleanJSONResponse(_ content: String) -> String {
        // Remove markdown code blocks
        var cleaned = content

        // Remove ```json and ``` markers
        cleaned = cleaned.replacingOccurrences(of: "```json", with: "")
        cleaned = cleaned.replacingOccurrences(of: "```", with: "")

        // Trim whitespace and newlines
        cleaned = cleaned.trimmingCharacters(in: .whitespacesAndNewlines)

        return cleaned
    }
}

// MARK: - Data Models
struct EnhancedProductInfo: Codable {
    let description: String
    let nutrition: String?
    let sustainability: String?
    let alternatives: String?
    let health: String?
    let usage: String?
}

struct ImageAnalysisResult: Codable {
    let productName: String?
    let brand: String?
    let category: String?
    let ingredients: String?
    let origin: String?
    let size: String?
    let certifications: String?
    let confidence: Int
}

struct ValidationResult: Codable {
    let isValid: Bool
    let confidence: Int
    let corrections: String?
    let suggestions: String?
    let inconsistencies: String?
}

struct LocalAlternatives: Codable {
    let localBrands: String?
    let similarProducts: String?
    let seasonal: String?
    let benefits: String?
    let whereToFind: String?
}

// MARK: - Gemini API Request/Response Models
struct GeminiRequest: Codable {
    let contents: [GeminiContent]
}

struct GeminiContent: Codable {
    let parts: [GeminiPart]
}

struct GeminiPart: Codable {
    let text: String?
    let inlineData: GeminiInlineData?
    
    init(text: String) {
        self.text = text
        self.inlineData = nil
    }
    
    init(inlineData: GeminiInlineData) {
        self.text = nil
        self.inlineData = inlineData
    }
}

struct GeminiInlineData: Codable {
    let mimeType: String
    let data: String
}

struct GeminiResponse: Codable {
    let candidates: [GeminiCandidate]
}

struct GeminiCandidate: Codable {
    let content: GeminiContent
}

// MARK: - Error Types
enum GeminiError: Error, LocalizedError {
    case apiKeyNotSet
    case invalidURL
    case encodingFailed
    case decodingFailed
    case invalidResponse
    case apiError(Int)
    case noContent
    case invalidJSONResponse
    case imageProcessingFailed
    
    var errorDescription: String? {
        switch self {
        case .apiKeyNotSet:
            return "Gemini API key not configured"
        case .invalidURL:
            return "Invalid Gemini API URL"
        case .encodingFailed:
            return "Failed to encode request"
        case .decodingFailed:
            return "Failed to decode response"
        case .invalidResponse:
            return "Invalid response from Gemini API"
        case .apiError(let code):
            return "Gemini API error: \(code)"
        case .noContent:
            return "No content in Gemini response"
        case .invalidJSONResponse:
            return "Invalid JSON in Gemini response"
        case .imageProcessingFailed:
            return "Failed to process image"
        }
    }
}
