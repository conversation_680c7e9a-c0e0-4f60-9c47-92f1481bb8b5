//
//  ProductClassifier.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-08.
//

import Foundation
import CoreML
import Vision
import UIKit
import AVFoundation
import CoreData

class ProductClassifier {
    // Singleton instance
    static let shared = ProductClassifier()

    // ML models for different tasks
    private var classificationModel: VNCoreMLModel?
    private var nutritionModel: VNCoreMLModel?
    private var barcodeDetector: VNDetectBarcodesRequest?
    private var generalClassifier: VNCoreMLModel?

    private init() {
        // Initialize the ML model
        setupModel()
    }

    private func setupModel() {
        print("Setting up machine learning models...")

        // Setup barcode detector
        barcodeDetector = VNDetectBarcodesRequest()
        if #available(iOS 15.0, *) {
            barcodeDetector?.symbologies = [.ean13, .ean8, .upce, .code128, .code39, .qr]
        } else {
            // For iOS 14 and earlier
            #if swift(>=5.5)
            barcodeDetector?.symbologies = [.EAN13, .EAN8, .UPCE, .code128, .code39, .QR]
            #else
            barcodeDetector?.symbologies = [.EAN13, .EAN8, .UPCE, .code128, .code39, .QR]
            #endif
        }

        // In a real implementation, we would load custom trained models
        // For now, we'll use Apple's built-in models through Vision framework

        // For food detection, we'll use Vision's built-in classification capabilities
        // No need to explicitly load a model for VNClassifyImageRequest

        // For product classification, we could use a custom model
        // Example:
        // do {
        //     let modelConfig = MLModelConfiguration()
        //     let model = try ProductClassifierModel(configuration: modelConfig)
        //     classificationModel = try VNCoreMLModel(for: model.model)
        // } catch {
        //     print("Failed to load classification model: \(error)")
        // }

        // For nutrition analysis, we could use a custom model
        // Example:
        // do {
        //     let modelConfig = MLModelConfiguration()
        //     let model = try NutritionAnalysisModel(configuration: modelConfig)
        //     nutritionModel = try VNCoreMLModel(for: model.model)
        // } catch {
        //     print("Failed to load nutrition model: \(error)")
        // }
    }

    // Method to classify a product from an image
    func classifyProduct(from image: UIImage, completion: @escaping (Result<[ClassificationResult], Error>) -> Void) {
        guard let cgImage = image.cgImage else {
            completion(.failure(ClassifierError.invalidImage))
            return
        }

        // First check if the image contains a food product
        checkIfFoodProduct(cgImage: cgImage) { [weak self] isFoodProduct in
            guard let self = self else { return }

            if !isFoodProduct {
                // Not a food product, return an appropriate error
                completion(.failure(ClassifierError.notFoodProduct))
                return
            }

            // It's a food product, now try to detect any barcodes in the image
            self.detectBarcodes(in: cgImage) { barcodeResult in
                switch barcodeResult {
                case .success(let barcodeValue):
                    if let barcode = barcodeValue {
                        print("Detected barcode: \(barcode)")
                        // Look up product info from barcode database
                        self.lookupProductFromBarcode(barcode) { productInfo in
                            if let product = productInfo {
                                // We have product info from the barcode
                                let results = self.convertProductInfoToClassification(product)
                                completion(.success(results))
                            } else {
                                // No product info found for barcode, fall back to image classification
                                self.performImageClassification(cgImage: cgImage, completion: completion)
                            }
                        }
                    } else {
                        // No barcode found, continue with image classification
                        self.performImageClassification(cgImage: cgImage, completion: completion)
                    }
                case .failure(let error):
                    print("Barcode detection failed: \(error)")
                    // Continue with image classification despite barcode error
                    self.performImageClassification(cgImage: cgImage, completion: completion)
                }
            }
        }
    }

    // Helper method to detect barcodes in an image
    private func detectBarcodes(in cgImage: CGImage, completion: @escaping (Result<String?, Error>) -> Void) {
        let requestHandler = VNImageRequestHandler(cgImage: cgImage, options: [:])

        guard let barcodeRequest = barcodeDetector else {
            completion(.failure(ClassifierError.modelNotLoaded))
            return
        }

        // Process the image for barcode detection
        do {
            try requestHandler.perform([barcodeRequest])

            // Check if any barcodes were found
            if let results = barcodeRequest.results, !results.isEmpty {
                // Get the first detected barcode
                if let barcode = results.first, let payload = barcode.payloadStringValue {
                    completion(.success(payload))
                    return
                }
            }

            // No barcodes found
            completion(.success(nil))
        } catch {
            completion(.failure(error))
        }
    }

    // Helper method to check if the image contains a food product
    private func checkIfFoodProduct(cgImage: CGImage, completion: @escaping (Bool) -> Void) {
        // Use Vision framework with a pre-trained model to identify food items

        // For demonstration purposes, we'll use a simple approach with VNClassifyImageRequest
        // which uses Apple's built-in image classification
        let request = VNClassifyImageRequest { request, error in
            guard error == nil else {
                print("General classification error: \(error!.localizedDescription)")
                completion(false)
                return
            }

            guard let results = request.results as? [VNClassificationObservation], !results.isEmpty else {
                completion(false)
                return
            }

            // Check if any of the top classifications are food-related
            let foodKeywords = ["food", "fruit", "vegetable", "tomato", "apple", "banana", "meat", "dairy", "bread", "grocery", "produce", "snack",
                               "beverage", "drink", "meal", "dish", "cuisine", "ingredient", "edible", "fresh", "organic", "natural", "raw"]

            // Get the top 10 classifications (increased from 5 to catch more potential matches)
            let topResults = results.prefix(10)

            // Print classifications for debugging
            for result in topResults {
                print("Classification: \(result.identifier), Confidence: \(result.confidence)")
            }

            // Check if any of the top results contain food-related keywords
            let isFoodProduct = topResults.contains { observation in
                let label = observation.identifier.lowercased()
                return foodKeywords.contains { keyword in label.contains(keyword) }
            }

            // Special case for common food items that might be misclassified
            let imageColors = self.analyzeColors(in: cgImage)

            // If it's predominantly red and round, it might be a tomato or apple
            if imageColors.isRedDominant && !isFoodProduct {
                print("Image appears to be red-dominant, might be a tomato or apple")
                completion(true)
                return
            }

            // For demo purposes, let's assume most images the user scans are food
            // In a real app, we'd have a more sophisticated model
            if !isFoodProduct && arc4random_uniform(100) < 90 {
                // 90% chance to consider it food anyway for testing
                print("Defaulting to food classification for testing")
                completion(true)
                return
            }

            completion(isFoodProduct)
        }

        // Configure the request
        request.revision = VNClassifyImageRequestRevision1

        // Create a request handler
        let requestHandler = VNImageRequestHandler(cgImage: cgImage, options: [:])

        // Perform the request
        do {
            try requestHandler.perform([request])
        } catch {
            print("Failed to perform classification: \(error)")
            completion(false)
        }
    }

    // Helper method to analyze dominant colors in an image
    private func analyzeColors(in cgImage: CGImage) -> (isRedDominant: Bool, isGreenDominant: Bool) {
        let width = cgImage.width
        let height = cgImage.height
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8

        var rawData = [UInt8](repeating: 0, count: width * height * bytesPerPixel)
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let context = CGContext(data: &rawData, width: width, height: height, bitsPerComponent: bitsPerComponent, bytesPerRow: bytesPerRow, space: colorSpace, bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue | CGBitmapInfo.byteOrder32Big.rawValue)

        context?.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        var redSum: Int = 0
        var greenSum: Int = 0
        var blueSum: Int = 0
        var pixelCount: Int = 0

        // Sample pixels (every 10th pixel to save processing time)
        for y in stride(from: 0, to: height, by: 10) {
            for x in stride(from: 0, to: width, by: 10) {
                let offset = (y * bytesPerRow) + (x * bytesPerPixel)
                redSum += Int(rawData[offset])
                greenSum += Int(rawData[offset + 1])
                blueSum += Int(rawData[offset + 2])
                pixelCount += 1
            }
        }

        // Calculate averages
        let redAvg = redSum / pixelCount
        let greenAvg = greenSum / pixelCount
        let blueAvg = blueSum / pixelCount

        print("Color analysis - R: \(redAvg), G: \(greenAvg), B: \(blueAvg)")

        // Determine if red is dominant (for tomatoes, apples, etc.)
        let isRedDominant = Double(redAvg) > Double(greenAvg) * 1.5 && Double(redAvg) > Double(blueAvg) * 1.5

        // Determine if green is dominant (for vegetables, etc.)
        let isGreenDominant = Double(greenAvg) > Double(redAvg) * 1.2 && Double(greenAvg) > Double(blueAvg) * 1.2

        return (isRedDominant, isGreenDominant)
    }

    // Helper method to look up product information from a barcode
    private func lookupProductFromBarcode(_ barcode: String, completion: @escaping (BarcodeProductInfo?) -> Void) {
        // In a real implementation, this would query a database or API
        // For now, we'll use a mock implementation

        // Simulate network delay
        DispatchQueue.global().asyncAfter(deadline: .now() + 0.5) {
            // For testing purposes, only return product info for specific barcodes
            if barcode == "0123456789012" {
                let productInfo = BarcodeProductInfo(
                    name: "Organic Apples",
                    brand: "Local Farms",
                    category: "Produce",
                    isOrganic: true,
                    isLocal: true,
                    origin: "United States",
                    packaging: "Recyclable"
                )
                DispatchQueue.main.async {
                    completion(productInfo)
                }
            } else {
                // No product info found for this barcode
                DispatchQueue.main.async {
                    completion(nil)
                }
            }
        }
    }

    // Helper method to convert product info to classification results
    private func convertProductInfoToClassification(_ productInfo: BarcodeProductInfo) -> [ClassificationResult] {
        var results: [ClassificationResult] = []

        // Add the product category with high confidence
        results.append(ClassificationResult(label: productInfo.category, confidence: 0.95))

        // Add organic classification if applicable
        if productInfo.isOrganic {
            results.append(ClassificationResult(label: "Organic", confidence: 0.90))
        }

        // Add local classification if applicable
        if productInfo.isLocal {
            results.append(ClassificationResult(label: "Local Produce", confidence: 0.85))
        } else {
            results.append(ClassificationResult(label: "Imported Goods", confidence: 0.80))
        }

        return results
    }

    // Helper method to perform image classification
    private func performImageClassification(cgImage: CGImage, completion: @escaping (Result<[ClassificationResult], Error>) -> Void) {
        // Use Vision framework for food classification
        let request = VNClassifyImageRequest { request, error in
            if let error = error {
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
                return
            }

            guard let observations = request.results as? [VNClassificationObservation], !observations.isEmpty else {
                DispatchQueue.main.async {
                    completion(.failure(ClassifierError.classificationFailed))
                }
                return
            }

            // Convert VNClassificationObservation to our ClassificationResult model
            let results = observations.prefix(6).map { observation in
                // Map common food categories to our app's categories
                var label = observation.identifier
                var confidence = observation.confidence

                // Map to more user-friendly categories
                if label.lowercased().contains("fruit") || label.lowercased().contains("vegetable") {
                    if label.lowercased().contains("organic") {
                        label = "Organic Vegetables"
                    } else {
                        label = "Local Produce"
                    }
                } else if label.lowercased().contains("packaged") || label.lowercased().contains("processed") {
                    label = "Packaged Food"
                    confidence = min(confidence, 0.6) // Lower confidence for processed foods
                } else if label.lowercased().contains("dairy") || label.lowercased().contains("milk") {
                    label = "Dairy Product"
                } else if label.lowercased().contains("imported") || label.lowercased().contains("foreign") {
                    label = "Imported Goods"
                }

                return ClassificationResult(label: label, confidence: Double(confidence))
            }

            // For demonstration, add some default categories if we don't have enough
            var finalResults = results
            if finalResults.count < 3 {
                // Add some default categories
                let defaultCategories = [
                    ClassificationResult(label: "Organic Vegetables", confidence: 0.72),
                    ClassificationResult(label: "Local Produce", confidence: 0.68),
                    ClassificationResult(label: "Packaged Food", confidence: 0.15)
                ]

                // Add only the missing ones
                let missingCount = 3 - finalResults.count
                finalResults.append(contentsOf: defaultCategories.prefix(missingCount))
            }

            DispatchQueue.main.async {
                completion(.success(finalResults))
            }
        }

        // Configure the request
        request.revision = VNClassifyImageRequestRevision1

        // Create a request handler
        let requestHandler = VNImageRequestHandler(cgImage: cgImage, options: [:])

        // Perform the request
        do {
            try requestHandler.perform([request])
        } catch {
            DispatchQueue.main.async {
                completion(.failure(error))
            }
        }
    }

    // Method to predict sustainability score based on product attributes
    func predictSustainabilityScore(productType: String, origin: String, packaging: String) -> Double {
        // This would use a trained model to predict sustainability
        // For now, we'll use a simple heuristic

        var score = 50.0 // Base score

        // Adjust for product type
        if productType.lowercased().contains("organic") {
            score += 15.0
        } else if productType.lowercased().contains("processed") {
            score -= 10.0
        }

        // Adjust for origin (local is better)
        if origin.lowercased().contains("united states") {
            score += 20.0
        } else if origin.lowercased().contains("europe") {
            score -= 5.0
        } else if origin.lowercased().contains("asia") || origin.lowercased().contains("china") {
            score -= 15.0
        }

        // Adjust for packaging
        if packaging.lowercased().contains("recyclable") {
            score += 10.0
        } else if packaging.lowercased().contains("plastic") {
            score -= 10.0
        }

        // Ensure score is between 0 and 100
        return max(0.0, min(100.0, score))
    }

    // Method to analyze nutritional content from an image
    func analyzeNutritionalContent(from image: UIImage, completion: @escaping (Result<NutritionalAnalysis, Error>) -> Void) {
        guard let cgImage = image.cgImage else {
            completion(.failure(ClassifierError.invalidImage))
            return
        }

        // First try to detect any text in the image (for ingredient lists)
        detectText(in: cgImage) { [weak self] textResult in
            var detectedIngredients: [String] = []
            var detectedAllergens: [String] = []

            if case .success(let text) = textResult, let detectedText = text {
                // Process the detected text to extract ingredients and allergens
                detectedIngredients = self?.extractIngredients(from: detectedText) ?? []
                detectedAllergens = self?.extractAllergens(from: detectedText) ?? []
            }

            // Simulate processing time for the nutrition analysis
            DispatchQueue.global().asyncAfter(deadline: .now() + 1.0) {
                // Enhanced mock nutritional analysis with the detected ingredients and allergens
                let analysis = NutritionalAnalysis(
                    calories: "240",
                    fat: "12g",
                    carbohydrates: "30g",
                    protein: "5g",
                    sugar: "15g",
                    fiber: "3g",
                    sodium: "120mg",
                    ingredients: detectedIngredients.isEmpty ?
                        ["Water", "Sugar", "Natural Flavors", "Citric Acid", "Potassium Benzoate"] : detectedIngredients,
                    allergens: detectedAllergens.isEmpty ?
                        ["May contain traces of nuts", "Produced in a facility that processes milk"] : detectedAllergens,
                    nutritionScore: 65,
                    healthRating: "B"
                )

                DispatchQueue.main.async {
                    completion(.success(analysis))
                }
            }
        }
    }

    // Helper method to detect text in an image
    private func detectText(in cgImage: CGImage, completion: @escaping (Result<String?, Error>) -> Void) {
        let request = VNRecognizeTextRequest { request, error in
            if let error = error {
                completion(.failure(error))
                return
            }

            guard let observations = request.results as? [VNRecognizedTextObservation] else {
                completion(.success(nil))
                return
            }

            // Combine all detected text
            let detectedText = observations.compactMap { observation in
                observation.topCandidates(1).first?.string
            }.joined(separator: " ")

            completion(.success(detectedText.isEmpty ? nil : detectedText))
        }

        // Configure the text recognition request
        request.recognitionLevel = .accurate

        let requestHandler = VNImageRequestHandler(cgImage: cgImage, options: [:])

        do {
            try requestHandler.perform([request])
        } catch {
            completion(.failure(error))
        }
    }

    // Helper method to extract ingredients from text
    private func extractIngredients(from text: String) -> [String] {
        // In a real implementation, this would use NLP to extract ingredients
        // For now, we'll use a simple approach

        // Check if the text contains an ingredients list
        let lowercasedText = text.lowercased()
        if lowercasedText.contains("ingredients") {
            // Try to extract the ingredients section
            if let range = lowercasedText.range(of: "ingredients") {
                let ingredientsText = text[range.upperBound...].trimmingCharacters(in: .whitespacesAndNewlines)

                // Split by commas and clean up
                return ingredientsText.components(separatedBy: ",")
                    .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
                    .filter { !$0.isEmpty }
            }
        }

        // If we couldn't extract ingredients, return an empty array
        return []
    }

    // Helper method to extract allergens from text
    private func extractAllergens(from text: String) -> [String] {
        // In a real implementation, this would use NLP to extract allergens
        // For now, we'll check for common allergen indicators

        let lowercasedText = text.lowercased()
        var allergens: [String] = []

        // Check for common allergen indicators
        let allergenKeywords = ["contains", "allergen", "may contain", "warning"]

        for keyword in allergenKeywords {
            if lowercasedText.contains(keyword) {
                if let range = lowercasedText.range(of: keyword) {
                    let allergenText = text[range.upperBound...].trimmingCharacters(in: .whitespacesAndNewlines)
                    allergens.append(allergenText)
                }
            }
        }

        // Check for common allergens
        let commonAllergens = ["peanuts", "tree nuts", "milk", "eggs", "fish", "shellfish", "soy", "wheat", "gluten"]

        for allergen in commonAllergens {
            if lowercasedText.contains(allergen) {
                allergens.append("Contains \(allergen)")
            }
        }

        return allergens
    }
}

// Models for classification results
struct ClassificationResult {
    let label: String
    let confidence: Double
}

// Product information from barcode lookup
struct BarcodeProductInfo {
    let name: String
    let brand: String
    let category: String
    let isOrganic: Bool
    let isLocal: Bool
    let origin: String
    let packaging: String
}

struct NutritionalAnalysis {
    let calories: String
    let fat: String
    let carbohydrates: String
    let protein: String
    let sugar: String
    let fiber: String
    let sodium: String
    let ingredients: [String]
    let allergens: [String]
    let nutritionScore: Int
    let healthRating: String
}

// Custom errors
enum ClassifierError: Error {
    case invalidImage
    case modelNotLoaded
    case classificationFailed
    case notFoodProduct
}
