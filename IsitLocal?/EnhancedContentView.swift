//
//  EnhancedContentView.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-08.
//

import SwiftUI
import CoreData

struct EnhancedContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject private var appState: AppState
    @State private var selectedTab: Int = 0
    @State private var showSecurityPrompt: Bool = false

    var body: some View {
        TabView(selection: $selectedTab) {
            // Scan tab
            ContentView()
                .tabItem {
                    Image(systemName: "barcode.viewfinder")
                    Text("Scan")
                }
                .tag(0)

            // Image Analysis tab
            ImageBasedScanView()
                .tabItem {
                    Image(systemName: "camera")
                    Text("Image Scan")
                }
                .tag(1)

            // Favorites tab
            FavoritesView()
                .tabItem {
                    Image(systemName: "star.fill")
                    Text("Favorites")
                }
                .tag(2)

            // Insights tab
            UserInsightsView()
                .tabItem {
                    Image(systemName: "chart.bar.fill")
                    Text("Insights")
                }
                .tag(3)

            // Settings tab
            SettingsView()
                .tabItem {
                    Image(systemName: "gear")
                    Text("Settings")
                }
                .tag(4)
        }
        .onAppear {
            // Track app performance
            PerformanceOptimizer.shared.trackAppLoaded()

            // Check if security is enabled and needs authentication
            if SecurityManager.shared.isSecurityEnabled && SecurityManager.shared.isBiometricEnabled {
                showSecurityPrompt = true
            }
        }
        .sheet(isPresented: $showSecurityPrompt) {
            BiometricAuthView { success in
                if !success {
                    // If authentication fails, limit functionality
                    selectedTab = 4 // Go to settings tab
                }
                showSecurityPrompt = false
            }
        }
    }
}

// Biometric authentication view
struct BiometricAuthView: View {
    var completion: (Bool) -> Void
    @State private var authenticationFailed = false
    @State private var errorMessage = ""

    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: getBiometricIcon())
                .font(.system(size: 60))
                .foregroundColor(.blue)
                .padding(.top, 50)

            Text("Authentication Required")
                .font(.title)
                .fontWeight(.bold)

            Text("Please authenticate to access the app")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)

            if authenticationFailed {
                Text(errorMessage)
                    .foregroundColor(.red)
                    .padding()
            }

            Button(action: authenticate) {
                Text("Authenticate")
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.blue)
                    .cornerRadius(10)
                    .padding(.horizontal)
            }
            .padding(.top, 20)

            Button(action: {
                // Skip authentication but limit functionality
                completion(false)
            }) {
                Text("Skip (Limited Access)")
                    .font(.subheadline)
                    .foregroundColor(.gray)
            }
            .padding(.top, 10)

            Spacer()
        }
        .padding()
        .onAppear {
            authenticate()
        }
    }

    private func authenticate() {
        SecurityManager.shared.authenticateWithBiometrics(reason: "Authenticate to access IsitLocal?") { success, error in
            if success {
                completion(true)
            } else {
                authenticationFailed = true
                if let error = error {
                    errorMessage = error.localizedDescription
                } else {
                    errorMessage = "Authentication failed"
                }
            }
        }
    }

    private func getBiometricIcon() -> String {
        let biometricStatus = SecurityManager.shared.isBiometricAvailable()
        switch biometricStatus.biometricType {
        case .faceID:
            return "faceid"
        case .touchID:
            return "touchid"
        case .none:
            return "lock.shield"
        }
    }
}

// Settings view
struct SettingsView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @State private var showingCacheAlert = false

    var body: some View {
        NavigationView {
            List {
                Section(header: Text("Account")) {
                    NavigationLink(destination: Text("Profile settings would go here")) {
                        HStack {
                            Image(systemName: "person.circle")
                                .foregroundColor(.blue)
                            Text("Profile")
                        }
                    }
                }

                Section(header: Text("Security")) {
                    NavigationLink(destination: SecuritySettingsView()) {
                        HStack {
                            Image(systemName: "lock.shield")
                                .foregroundColor(.blue)
                            Text("Security Settings")
                        }
                    }
                }

                Section(header: Text("Data Management")) {
                    Button(action: {
                        showingCacheAlert = true
                    }) {
                        HStack {
                            Image(systemName: "trash")
                                .foregroundColor(.red)
                            Text("Clear Cache")
                        }
                    }

                    NavigationLink(destination: OfflineModeView()) {
                        HStack {
                            Image(systemName: "arrow.down.circle")
                                .foregroundColor(.blue)
                            Text("Offline Products")
                        }
                    }
                }

                Section(header: Text("Contribute")) {
                    NavigationLink(destination: UserContributionView(barcode: nil, productName: nil)) {
                        HStack {
                            Image(systemName: "hand.raised")
                                .foregroundColor(.blue)
                            Text("Contribute Data")
                        }
                    }
                }

                Section(header: Text("About")) {
                    HStack {
                        Text("Version")
                        Spacer()
                        Text("1.0.0")
                            .foregroundColor(.gray)
                    }

                    NavigationLink(destination: Text("Privacy policy would go here")) {
                        Text("Privacy Policy")
                    }

                    NavigationLink(destination: Text("Terms of service would go here")) {
                        Text("Terms of Service")
                    }
                }
            }
            .navigationTitle("Settings")
            .alert(isPresented: $showingCacheAlert) {
                Alert(
                    title: Text("Clear Cache"),
                    message: Text("This will clear all cached product data. This action cannot be undone."),
                    primaryButton: .destructive(Text("Clear")) {
                        clearCache()
                    },
                    secondaryButton: .cancel()
                )
            }
        }
    }

    private func clearCache() {
        PerformanceOptimizer.shared.cleanupCache(context: viewContext)
    }
}
