//
//  OfflineManager.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-02.
//

import Foundation
import CoreData

class OfflineManager {
    private let context: NSManagedObjectContext
    
    init(context: NSManagedObjectContext) {
        self.context = context
    }
    
    // Function to save a product for offline use
    func saveProductForOffline(barcode: String, productName: String, origin: String) {
        let offlineProduct = OfflineProduct(context: context)
        offlineProduct.barcode = barcode
        offlineProduct.productName = productName
        offlineProduct.origin = origin
        offlineProduct.savedDate = Date()
        
        do {
            try context.save()
            print("Saved product for offline use: \(productName)")
        } catch {
            print("Failed to save product for offline use: \(error)")
        }
    }
    
    // Function to get all offline products
    func getOfflineProducts() -> [OfflineProduct] {
        let fetchRequest: NSFetchRequest<OfflineProduct> = OfflineProduct.fetchRequest()
        fetchRequest.sortDescriptors = [NSSortDescriptor(keyPath: \OfflineProduct.savedDate, ascending: false)]
        
        do {
            return try context.fetch(fetchRequest)
        } catch {
            print("Failed to fetch offline products: \(error)")
            return []
        }
    }
    
    // Function to delete an offline product
    func deleteOfflineProduct(product: OfflineProduct) {
        context.delete(product)
        
        do {
            try context.save()
            print("Deleted offline product: \(product.productName ?? "Unknown")")
        } catch {
            print("Failed to delete offline product: \(error)")
        }
    }
}
