//
//  BarcodeOriginDatabase.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-02.
//

import Foundation

// This class contains the database of barcode prefixes and their corresponding countries
class BarcodeOriginDatabase {
    
    // Function to get the country from the barcode prefix
    static func getOriginFromPrefix(_ prefix: Int) -> String {
        switch prefix {
        case 000...019:
            return "United States 🇺🇸"
        case 020...029:
            return "In-store use (Restricted distribution)"
        case 030...039:
            return "United States 🇺🇸"
        case 040...049:
            return "In-store use (Restricted distribution)"
        case 050...059:
            return "Coupons"
        case 060...099:
            return "United States 🇺🇸"
        case 100...139:
            return "United States 🇺🇸"
        case 200...299:
            return "In-store use (Restricted distribution)"
        case 300...379:
            return "France 🇫🇷"
        case 380:
            return "Bulgaria 🇧🇬"
        case 383:
            return "Slovenia 🇸🇮"
        case 385:
            return "Croatia 🇭🇷"
        case 387:
            return "Bosnia and Herzegovina 🇧🇦"
        case 389:
            return "Montenegro 🇲🇪"
        case 400...440:
            return "Germany 🇩🇪"
        case 450...459:
            return "Japan 🇯🇵"
        case 460...469:
            return "Russia 🇷🇺"
        case 470:
            return "Kyrgyzstan 🇰🇬"
        case 471:
            return "Taiwan 🇹🇼"
        case 474:
            return "Estonia 🇪🇪"
        case 475:
            return "Latvia 🇱🇻"
        case 476:
            return "Azerbaijan 🇦🇿"
        case 477:
            return "Lithuania 🇱🇹"
        case 478:
            return "Uzbekistan 🇺🇿"
        case 479:
            return "Sri Lanka 🇱🇰"
        case 480:
            return "Philippines 🇵🇭"
        case 481:
            return "Belarus 🇧🇾"
        case 482:
            return "Ukraine 🇺🇦"
        case 483:
            return "Turkmenistan 🇹🇲"
        case 484:
            return "Moldova 🇲🇩"
        case 485:
            return "Armenia 🇦🇲"
        case 486:
            return "Georgia 🇬🇪"
        case 487:
            return "Kazakhstan 🇰🇿"
        case 488:
            return "Tajikistan 🇹🇯"
        case 489:
            return "Hong Kong 🇭🇰"
        case 490...499:
            return "Japan 🇯🇵"
        case 500...509:
            return "United Kingdom 🇬🇧"
        case 520...521:
            return "Greece 🇬🇷"
        case 528:
            return "Lebanon 🇱🇧"
        case 529:
            return "Cyprus 🇨🇾"
        case 530:
            return "Albania 🇦🇱"
        case 531:
            return "North Macedonia 🇲🇰"
        case 535:
            return "Malta 🇲🇹"
        case 539:
            return "Ireland 🇮🇪"
        case 540...549:
            return "Belgium & Luxembourg 🇧🇪🇱🇺"
        case 560:
            return "Portugal 🇵🇹"
        case 569:
            return "Iceland 🇮🇸"
        case 570...579:
            return "Denmark 🇩🇰"
        case 590:
            return "Poland 🇵🇱"
        case 594:
            return "Romania 🇷🇴"
        case 599:
            return "Hungary 🇭🇺"
        case 600...601:
            return "South Africa 🇿🇦"
        case 603:
            return "Ghana 🇬🇭"
        case 604:
            return "Senegal 🇸🇳"
        case 608:
            return "Bahrain 🇧🇭"
        case 609:
            return "Mauritius 🇲🇺"
        case 611:
            return "Morocco 🇲🇦"
        case 613:
            return "Algeria 🇩🇿"
        case 615:
            return "Nigeria 🇳🇬"
        case 616:
            return "Kenya 🇰🇪"
        case 617:
            return "Cameroon 🇨🇲"
        case 618:
            return "Ivory Coast 🇨🇮"
        case 619:
            return "Tunisia 🇹🇳"
        case 620:
            return "Tanzania 🇹🇿"
        case 621:
            return "Syria 🇸🇾"
        case 622:
            return "Egypt 🇪🇬"
        case 623:
            return "Brunei 🇧🇳"
        case 624:
            return "Libya 🇱🇾"
        case 625:
            return "Jordan 🇯🇴"
        case 626:
            return "Iran 🇮🇷"
        case 627:
            return "Kuwait 🇰🇼"
        case 628:
            return "Saudi Arabia 🇸🇦"
        case 629:
            return "United Arab Emirates 🇦🇪"
        case 630:
            return "Qatar 🇶🇦"
        case 631:
            return "Namibia 🇳🇦"
        case 632:
            return "Zimbabwe 🇿🇼"
        case 633:
            return "Seychelles 🇸🇨"
        case 634:
            return "Sudan 🇸🇩"
        case 635:
            return "Ethiopia 🇪🇹"
        case 636:
            return "Eritrea 🇪🇷"
        case 637:
            return "Djibouti 🇩🇯"
        case 638:
            return "South Sudan 🇸🇸"
        case 639:
            return "Uganda 🇺🇬"
        case 640...649:
            return "Finland 🇫🇮"
        case 690...699:
            return "China 🇨🇳"
        case 700...709:
            return "Norway 🇳🇴"
        case 729:
            return "Israel 🇮🇱"
        case 730...739:
            return "Sweden 🇸🇪"
        case 740:
            return "Guatemala 🇬🇹"
        case 741:
            return "El Salvador 🇸🇻"
        case 742:
            return "Honduras 🇭🇳"
        case 743:
            return "Nicaragua 🇳🇮"
        case 744:
            return "Costa Rica 🇨🇷"
        case 745:
            return "Panama 🇵🇦"
        case 746:
            return "Dominican Republic 🇩🇴"
        case 750:
            return "Mexico 🇲🇽"
        case 754...759:
            return "Canada 🇨🇦"
        case 760...769:
            return "Switzerland 🇨🇭"
        case 770...771:
            return "Colombia 🇨🇴"
        case 773:
            return "Uruguay 🇺🇾"
        case 775:
            return "Peru 🇵🇪"
        case 777:
            return "Bolivia 🇧🇴"
        case 778...779:
            return "Argentina 🇦🇷"
        case 780:
            return "Chile 🇨🇱"
        case 784:
            return "Paraguay 🇵🇾"
        case 786:
            return "Ecuador 🇪🇨"
        case 789...790:
            return "Brazil 🇧🇷"
        case 800...839:
            return "Italy 🇮🇹"
        case 840...849:
            return "Spain 🇪🇸"
        case 850:
            return "Cuba 🇨🇺"
        case 858:
            return "Slovakia 🇸🇰"
        case 859:
            return "Czech Republic 🇨🇿"
        case 860:
            return "Serbia 🇷🇸"
        case 865:
            return "Mongolia 🇲🇳"
        case 867:
            return "North Korea 🇰🇵"
        case 868...869:
            return "Turkey 🇹🇷"
        case 870...879:
            return "Netherlands 🇳🇱"
        case 880:
            return "South Korea 🇰🇷"
        case 881:
            return "Bangladesh 🇧🇩"
        case 883:
            return "Myanmar 🇲🇲"
        case 884:
            return "Cambodia 🇰🇭"
        case 885:
            return "Thailand 🇹🇭"
        case 886:
            return "Singapore 🇸🇬"
        case 888:
            return "Malaysia 🇲🇾"
        case 890:
            return "India 🇮🇳"
        case 893:
            return "Vietnam 🇻🇳"
        case 896:
            return "Pakistan 🇵🇰"
        case 897:
            return "Nepal 🇳🇵"
        case 899:
            return "Indonesia 🇮🇩"
        case 900...919:
            return "Austria 🇦🇹"
        case 930...939:
            return "Australia 🇦🇺"
        case 940...949:
            return "New Zealand 🇳🇿"
        case 950:
            return "Global Office (Special Use)"
        case 951:
            return "Global Office (Special Use)"
        case 955:
            return "Malaysia 🇲🇾"
        case 958:
            return "Macau 🇲🇴"
        case 960:
            return "Gabon 🇬🇦"
        case 961:
            return "Global Office (Special Use)"
        case 962:
            return "Global Office (Special Use)"
        case 963:
            return "Global Office (Special Use)"
        case 964:
            return "Global Office (Special Use)"
        case 965:
            return "Global Office (Special Use)"
        case 966:
            return "Global Office (Special Use)"
        case 967:
            return "Global Office (Special Use)"
        case 968:
            return "Global Office (Special Use)"
        case 969:
            return "Global Office (Special Use)"
        case 970:
            return "Global Office (Special Use)"
        case 971:
            return "Global Office (Special Use)"
        case 972:
            return "Global Office (Special Use)"
        case 973:
            return "Global Office (Special Use)"
        case 974:
            return "Global Office (Special Use)"
        case 975:
            return "Global Office (Special Use)"
        case 976:
            return "Global Office (Special Use)"
        case 977:
            return "Periodicals (ISSN)"
        case 978...979:
            return "Bookland (ISBN)"
        case 980:
            return "Refund Receipts"
        case 981...984:
            return "Common Currency Coupons"
        case 985...989:
            return "Common Currency Coupons"
        case 990...999:
            return "Coupons"
        default:
            return "Unknown"
        }
    }
}
