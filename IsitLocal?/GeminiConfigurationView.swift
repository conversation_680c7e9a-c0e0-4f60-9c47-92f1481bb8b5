//
//  GeminiConfigurationView.swift
//  IsitLocal?
//
//  Created by Augment Agent on 2025-06-11.
//  Configuration view for Gemini API key setup
//

import SwiftUI

struct GeminiConfigurationView: View {
    @State private var apiKey: String = ""
    @State private var isConfigured: Bool = false
    @State private var showingAlert: Bool = false
    @State private var alertMessage: String = ""
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // Header
                VStack(spacing: 15) {
                    Image(systemName: "brain.head.profile")
                        .font(.system(size: 60))
                        .foregroundColor(.orange)
                    
                    Text("Gemini AI Configuration")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("Configure your free Gemini API key to unlock enhanced product information, image recognition, and data validation.")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                
                // Configuration form
                VStack(alignment: .leading, spacing: 20) {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Gemini API Key")
                            .font(.headline)
                        
                        SecureField("Enter your free Gemini API key", text: $apiKey)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .autocapitalization(.none)
                            .disableAutocorrection(true)
                        
                        Text("Get your free API key from Google AI Studio")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // Status indicator
                    HStack {
                        Image(systemName: isConfigured ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(isConfigured ? .green : .red)
                        
                        Text(isConfigured ? "API Key Configured" : "API Key Not Set")
                            .font(.subheadline)
                            .foregroundColor(isConfigured ? .green : .red)
                    }
                    
                    // Features list
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Features Enabled:")
                            .font(.headline)
                        
                        FeatureRow(icon: "info.circle.fill", title: "Enhanced Product Information", description: "Get detailed descriptions and insights")
                        
                        FeatureRow(icon: "eye.fill", title: "Advanced Image Recognition", description: "Analyze product images with AI")
                        
                        FeatureRow(icon: "checkmark.shield.fill", title: "Data Validation", description: "Verify and enhance product data accuracy")
                        
                        FeatureRow(icon: "location.fill", title: "Local Alternatives", description: "Find local products and alternatives")
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
                
                Spacer()
                
                // Action buttons
                VStack(spacing: 15) {
                    Button(action: saveConfiguration) {
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                            Text("Save Configuration")
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(apiKey.isEmpty ? Color.gray : Color.blue)
                        .cornerRadius(12)
                    }
                    .disabled(apiKey.isEmpty)
                    
                    Button(action: openGoogleAIStudio) {
                        HStack {
                            Image(systemName: "link")
                            Text("Get Free API Key")
                        }
                        .font(.subheadline)
                        .foregroundColor(.blue)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(12)
                    }
                }
            }
            .padding()
            .navigationTitle("AI Configuration")
            .navigationBarItems(
                leading: Button("Cancel") {
                    presentationMode.wrappedValue.dismiss()
                },
                trailing: Button("Done") {
                    presentationMode.wrappedValue.dismiss()
                }
                .disabled(!isConfigured)
            )
            .onAppear {
                loadCurrentConfiguration()
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("Configuration"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
    }
    
    private func loadCurrentConfiguration() {
        // Check if API key is already configured
        // In a real app, you'd load this from UserDefaults or Keychain
        let currentKey = UserDefaults.standard.string(forKey: "GeminiAPIKey") ?? ""
        if !currentKey.isEmpty && currentKey != "YOUR_FREE_GEMINI_API_KEY_HERE" {
            apiKey = currentKey
            isConfigured = true
        }
    }
    
    private func saveConfiguration() {
        guard !apiKey.isEmpty else { return }
        
        // Save to UserDefaults (in production, use Keychain for security)
        UserDefaults.standard.set(apiKey, forKey: "GeminiAPIKey")
        
        // Configure the Gemini API manager
        GeminiAPIManager.shared.setAPIKey(apiKey)
        
        isConfigured = true
        alertMessage = "Gemini API key configured successfully! You can now use AI-enhanced features."
        showingAlert = true
    }
    
    private func openGoogleAIStudio() {
        if let url = URL(string: "https://makersuite.google.com/app/apikey") {
            UIApplication.shared.open(url)
        }
    }
}

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

#Preview {
    GeminiConfigurationView()
}
