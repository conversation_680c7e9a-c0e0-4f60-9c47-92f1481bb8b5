//
//  SplashView.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-02.
//

import SwiftUI

struct SplashView: View {
    // Animation properties
    @State private var scale: CGFloat = 0.8
    @State private var opacity: Double = 0.6
    
    var body: some View {
        ZStack {
            // Background
            Color.black.opacity(0.3)
                .edgesIgnoringSafeArea(.all)
            
            // Content
            VStack(spacing: 20) {
                // App logo
                Image(systemName: "barcode.viewfinder")
                    .font(.system(size: 80))
                    .foregroundColor(.white)
                
                // App name
                Text("Is It Local?")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                // Loading indicator
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(1.5)
                    .padding(.top, 20)
            }
            .scaleEffect(scale)
            .opacity(opacity)
            .onAppear {
                // Animate the splash screen
                withAnimation(Animation.easeInOut(duration: 0.8).repeatForever(autoreverses: true)) {
                    self.scale = 0.9
                    self.opacity = 1.0
                }
            }
        }
        .transition(.opacity)
    }
}
