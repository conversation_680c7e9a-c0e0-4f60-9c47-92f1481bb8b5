//
//  SecurityManager.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-08.
//

import Foundation
import CryptoKit
import LocalAuthentication

class SecurityManager {
    // Singleton instance
    static let shared = SecurityManager()

    // Keys for UserDefaults
    private let securityEnabledKey = "com.isitlocal.securityEnabled"
    private let biometricEnabledKey = "com.isitlocal.biometricEnabled"
    private let lastAuthTimeKey = "com.isitlocal.lastAuthTime"

    // Authentication context
    private let authContext = LAContext()

    private init() {
        // Initialize security settings if needed
        if UserDefaults.standard.object(forKey: securityEnabledKey) == nil {
            UserDefaults.standard.set(false, forKey: securityEnabledKey)
        }

        if UserDefaults.standard.object(forKey: biometricEnabledKey) == nil {
            UserDefaults.standard.set(false, forKey: biometricEnabledKey)
        }
    }

    // Check if security is enabled
    var isSecurityEnabled: Bool {
        return UserDefaults.standard.bool(forKey: securityEnabledKey)
    }

    // Check if biometric authentication is enabled
    var isBiometricEnabled: Bool {
        return UserDefaults.standard.bool(forKey: biometricEnabledKey)
    }

    // Enable or disable security
    func setSecurityEnabled(_ enabled: Bool) {
        UserDefaults.standard.set(enabled, forKey: securityEnabledKey)
    }

    // Enable or disable biometric authentication
    func setBiometricEnabled(_ enabled: Bool) {
        UserDefaults.standard.set(enabled, forKey: biometricEnabledKey)
    }

    // Check if biometric authentication is available
    func isBiometricAvailable() -> (available: Bool, biometricType: BiometricType, error: Error?) {
        var error: NSError?
        let canEvaluate = authContext.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error)

        var biometricType: BiometricType = .none
        if canEvaluate {
            if #available(iOS 11.0, *) {
                switch authContext.biometryType {
                case .faceID:
                    biometricType = .faceID
                case .touchID:
                    biometricType = .touchID
                default:
                    biometricType = .none
                }
            } else {
                biometricType = .touchID
            }
        }

        return (canEvaluate, biometricType, error)
    }

    // Authenticate user with biometrics
    func authenticateWithBiometrics(reason: String, completion: @escaping (Bool, Error?) -> Void) {
        guard isBiometricEnabled else {
            completion(true, nil) // Skip authentication if not enabled
            return
        }

        // Check if we've authenticated recently (within 5 minutes)
        if let lastAuthTime = UserDefaults.standard.object(forKey: lastAuthTimeKey) as? Date,
           Date().timeIntervalSince(lastAuthTime) < 300 {
            // Recently authenticated, skip
            completion(true, nil)
            return
        }

        authContext.evaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, localizedReason: reason) { success, error in
            if success {
                // Store authentication time
                UserDefaults.standard.set(Date(), forKey: self.lastAuthTimeKey)
            }

            DispatchQueue.main.async {
                completion(success, error)
            }
        }
    }

    // Hash sensitive data
    func hashData(_ data: String) -> String {
        let inputData = Data(data.utf8)
        let hashed = SHA256.hash(data: inputData)
        return hashed.compactMap { String(format: "%02x", $0) }.joined()
    }

    // Encrypt data
    func encryptData(_ data: String, with key: String) -> Data? {
        guard let dataToEncrypt = data.data(using: .utf8),
              key.data(using: .utf8) != nil else {
            return nil
        }

        // In a real implementation, this would use proper encryption
        // For now, we'll just return the data
        return dataToEncrypt
    }

    // Decrypt data
    func decryptData(_ data: Data, with key: String) -> String? {
        guard key.data(using: .utf8) != nil else {
            return nil
        }

        // In a real implementation, this would use proper decryption
        // For now, we'll just return the data as a string
        return String(data: data, encoding: .utf8)
    }

    // Secure user data before sending to server
    func secureDataForTransmission(_ data: [String: Any]) -> [String: Any] {
        var securedData = data

        // Remove any sensitive information
        securedData.removeValue(forKey: "userID")
        securedData.removeValue(forKey: "password")
        securedData.removeValue(forKey: "token")

        // Add a timestamp for security
        securedData["timestamp"] = Date().timeIntervalSince1970

        return securedData
    }

    // Validate incoming data
    func validateIncomingData(_ data: [String: Any]) -> Bool {
        // Check for required fields
        guard let timestamp = data["timestamp"] as? TimeInterval else {
            return false
        }

        // Check if data is recent (within 5 minutes)
        let currentTime = Date().timeIntervalSince1970
        if currentTime - timestamp > 300 {
            return false
        }

        // Additional validation could be performed here

        return true
    }
}

// Enum for biometric types
enum BiometricType {
    case none
    case touchID
    case faceID
}
