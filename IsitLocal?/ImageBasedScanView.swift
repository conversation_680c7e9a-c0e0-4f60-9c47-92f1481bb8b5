//
//  ImageBasedScanView.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-08.
//

import SwiftUI
import UIKit

struct ImageBasedScanView: View {
    @State private var showImagePicker = false
    @State private var inputImage: UIImage?
    @State private var isAnalyzing = false
    @State private var classificationResults: [ClassificationResult] = []
    @State private var nutritionalAnalysis: NutritionalAnalysis?
    @State private var sustainabilityScore: Double = 0
    @State private var showSustainabilityInfo: Bool = false
    @State private var errorMessage: String?

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    Text("Image Analysis")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .padding(.top, 20)

                    // Image selection area
                    VStack {
                        if let inputImage = inputImage {
                            Image(uiImage: inputImage)
                                .resizable()
                                .scaledToFit()
                                .frame(maxHeight: 300)
                                .clipShape(RoundedRectangle(cornerRadius: 10))
                                .shadow(radius: 5)
                        } else {
                            ZStack {
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(Color.gray.opacity(0.2))
                                    .frame(height: 200)

                                VStack {
                                    Image(systemName: "photo")
                                        .font(.system(size: 40))

                                    Text("Tap to select an image")
                                        .font(.headline)
                                        .padding(.top, 10)
                                }
                                .foregroundColor(.gray)
                            }
                        }
                    }
                    .onTapGesture {
                        showImagePicker = true
                    }
                    .padding(.horizontal)

                    // Analyze button
                    Button(action: analyzeImage) {
                        HStack {
                            Image(systemName: "magnifyingglass")
                            Text("Analyze Image")
                        }
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(inputImage == nil ? Color.gray : Color.blue)
                        .foregroundColor(.white)
                        .clipShape(Capsule())
                        .shadow(radius: 3)
                    }
                    .disabled(inputImage == nil || isAnalyzing)
                    .padding(.horizontal)

                    if isAnalyzing {
                        ProgressView("Analyzing image...")
                            .progressViewStyle(CircularProgressViewStyle())
                            .padding()
                    }

                    if let errorMessage = errorMessage {
                        Text(errorMessage)
                            .foregroundColor(.red)
                            .padding()
                    }

                    // Results section
                    if !classificationResults.isEmpty {
                        VStack(alignment: .leading, spacing: 15) {
                            Text("Product Classification")
                                .font(.headline)
                                .padding(.bottom, 5)

                            ForEach(classificationResults, id: \.label) { result in
                                HStack {
                                    Text(result.label)
                                        .font(.subheadline)

                                    Spacer()

                                    Text("\(Int(result.confidence * 100))%")
                                        .font(.subheadline)
                                        .foregroundColor(.gray)
                                }

                                ProgressView(value: result.confidence)
                                    .progressViewStyle(LinearProgressViewStyle())
                                    .tint(confidenceColor(for: result.confidence))
                            }
                        }
                        .padding()
                        .background(Color.white.opacity(0.9))
                        .clipShape(RoundedRectangle(cornerRadius: 20))
                        .shadow(radius: 5)
                        .padding(.horizontal)
                    }

                    // Nutritional analysis section
                    if let analysis = nutritionalAnalysis {
                        VStack(alignment: .leading, spacing: 15) {
                            HStack {
                                Text("Nutritional Analysis")
                                    .font(.headline)

                                Spacer()

                                // Health rating badge
                                ZStack {
                                    Circle()
                                        .fill(healthRatingColor(for: analysis.healthRating))
                                        .frame(width: 40, height: 40)

                                    Text(analysis.healthRating)
                                        .font(.system(size: 20, weight: .bold))
                                        .foregroundColor(.white)
                                }
                            }
                            .padding(.bottom, 5)

                            // Nutrition score
                            HStack {
                                Text("Nutrition Score:")
                                    .font(.subheadline)

                                Spacer()

                                Text("\(analysis.nutritionScore)/100")
                                    .font(.subheadline)
                                    .foregroundColor(nutritionScoreColor(for: analysis.nutritionScore))
                            }

                            // Progress bar for nutrition score
                            ProgressView(value: Double(analysis.nutritionScore) / 100.0)
                                .progressViewStyle(LinearProgressViewStyle())
                                .tint(nutritionScoreColor(for: analysis.nutritionScore))
                                .padding(.bottom, 10)

                            // Nutrition facts
                            NutritionRow(title: "Calories", value: analysis.calories)
                            NutritionRow(title: "Fat", value: analysis.fat)
                            NutritionRow(title: "Carbohydrates", value: analysis.carbohydrates)
                            NutritionRow(title: "Protein", value: analysis.protein)
                            NutritionRow(title: "Sugar", value: analysis.sugar)
                            NutritionRow(title: "Fiber", value: analysis.fiber)
                            NutritionRow(title: "Sodium", value: analysis.sodium)

                            Divider()

                            // Ingredients section
                            Text("Detected Ingredients:")
                                .font(.subheadline)
                                .fontWeight(.bold)
                                .padding(.top, 5)

                            ForEach(analysis.ingredients, id: \.self) { ingredient in
                                HStack {
                                    Image(systemName: "circle.fill")
                                        .font(.system(size: 6))
                                        .padding(.trailing, 5)

                                    Text(ingredient)
                                        .font(.subheadline)
                                }
                            }

                            // Allergens section
                            if !analysis.allergens.isEmpty {
                                Divider()

                                Text("Allergen Information:")
                                    .font(.subheadline)
                                    .fontWeight(.bold)
                                    .padding(.top, 5)
                                    .foregroundColor(.red)

                                ForEach(analysis.allergens, id: \.self) { allergen in
                                    HStack {
                                        Image(systemName: "exclamationmark.triangle.fill")
                                            .foregroundColor(.red)
                                            .font(.system(size: 12))
                                            .padding(.trailing, 5)

                                        Text(allergen)
                                            .font(.subheadline)
                                            .foregroundColor(.red)
                                    }
                                }
                            }
                        }
                        .padding()
                        .background(Color.white.opacity(0.9))
                        .clipShape(RoundedRectangle(cornerRadius: 20))
                        .shadow(radius: 5)
                        .padding(.horizontal)
                    }

                    // Sustainability analysis section
                    if showSustainabilityInfo {
                        VStack(alignment: .leading, spacing: 15) {
                            Text("Sustainability Analysis")
                                .font(.headline)
                                .padding(.bottom, 5)

                            // Sustainability score
                            HStack {
                                Text("Environmental Impact:")
                                    .font(.subheadline)

                                Spacer()

                                Text(sustainabilityRating(for: sustainabilityScore))
                                    .font(.subheadline)
                                    .foregroundColor(sustainabilityColor(for: sustainabilityScore))
                            }

                            // Progress bar for sustainability score
                            ProgressView(value: sustainabilityScore / 100.0)
                                .progressViewStyle(LinearProgressViewStyle())
                                .tint(sustainabilityColor(for: sustainabilityScore))
                                .padding(.bottom, 10)

                            // Sustainability details
                            VStack(alignment: .leading, spacing: 10) {
                                Text("Based on our analysis:")
                                    .font(.subheadline)
                                    .fontWeight(.bold)

                                ForEach(sustainabilityFactors(for: sustainabilityScore), id: \.self) { factor in
                                    HStack(alignment: .top) {
                                        Image(systemName: "leaf.fill")
                                            .foregroundColor(.green)
                                            .font(.system(size: 12))
                                            .padding(.trailing, 5)

                                        Text(factor)
                                            .font(.subheadline)
                                    }
                                }
                            }

                            Divider()

                            // Recommendations
                            Text("Recommendations:")
                                .font(.subheadline)
                                .fontWeight(.bold)
                                .padding(.top, 5)

                            ForEach(sustainabilityRecommendations(for: sustainabilityScore), id: \.self) { recommendation in
                                HStack(alignment: .top) {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(.blue)
                                        .font(.system(size: 12))
                                        .padding(.trailing, 5)

                                    Text(recommendation)
                                        .font(.subheadline)
                                }
                            }
                        }
                        .padding()
                        .background(Color.white.opacity(0.9))
                        .clipShape(RoundedRectangle(cornerRadius: 20))
                        .shadow(radius: 5)
                        .padding(.horizontal)
                    }
                }
                .padding(.bottom, 20)
            }
            .background(
                LinearGradient(gradient: Gradient(colors: [.blue.opacity(0.3), .green.opacity(0.3)]), startPoint: .top, endPoint: .bottom)
                    .ignoresSafeArea()
            )
            .navigationTitle("Image Analysis")
            .sheet(isPresented: $showImagePicker) {
                ImagePicker(image: $inputImage)
            }
        }
    }

    private func analyzeImage() {
        guard let image = inputImage else { return }

        isAnalyzing = true
        errorMessage = nil
        classificationResults = []
        nutritionalAnalysis = nil
        sustainabilityScore = 0
        showSustainabilityInfo = false

        // First try Gemini API for enhanced image analysis
        Task {
            do {
                let geminiResult = try await GeminiAPIManager.shared.analyzeProductImage(image, barcode: nil)

                // Convert Gemini results to classification format
                var geminiClassifications: [ClassificationResult] = []

                if let productName = geminiResult.productName {
                    geminiClassifications.append(ClassificationResult(
                        label: "Product: \(productName)",
                        confidence: Double(geminiResult.confidence) / 10.0
                    ))
                }

                if let brand = geminiResult.brand {
                    geminiClassifications.append(ClassificationResult(
                        label: "Brand: \(brand)",
                        confidence: Double(geminiResult.confidence) / 10.0
                    ))
                }

                if let category = geminiResult.category {
                    geminiClassifications.append(ClassificationResult(
                        label: "Category: \(category)",
                        confidence: Double(geminiResult.confidence) / 10.0
                    ))
                }

                if let origin = geminiResult.origin {
                    geminiClassifications.append(ClassificationResult(
                        label: "Origin: \(origin)",
                        confidence: Double(geminiResult.confidence) / 10.0
                    ))
                }

                DispatchQueue.main.async {
                    self.classificationResults = geminiClassifications
                    self.calculateSustainabilityScore(from: geminiClassifications)
                    self.showSustainabilityInfo = true
                    self.isAnalyzing = false
                }

                print("Gemini image analysis successful - Confidence: \(geminiResult.confidence)/10")

            } catch {
                print("Gemini image analysis failed: \(error.localizedDescription)")

                // Fallback to original ProductClassifier
                DispatchQueue.main.async {
                    self.analyzeImageWithProductClassifier(image)
                }
            }
        }
    }

    private func analyzeImageWithProductClassifier(_ image: UIImage) {
        // Analyze the image using ProductClassifier as fallback
        ProductClassifier.shared.classifyProduct(from: image) { result in

            switch result {
            case .success(let classifications):
                self.classificationResults = classifications

                // Calculate sustainability score based on classifications
                self.calculateSustainabilityScore(from: classifications)

                // After classification, analyze nutritional content
                ProductClassifier.shared.analyzeNutritionalContent(from: image) { nutritionResult in
                    self.isAnalyzing = false

                    switch nutritionResult {
                    case .success(let analysis):
                        self.nutritionalAnalysis = analysis

                        // Show sustainability info after all analysis is complete
                        self.showSustainabilityInfo = true

                    case .failure(let error):
                        self.errorMessage = "Failed to analyze nutrition: \(error.localizedDescription)"
                    }
                }

            case .failure(let error):
                self.isAnalyzing = false

                if let classifierError = error as? ClassifierError, classifierError == .notFoodProduct {
                    self.errorMessage = "This doesn't appear to be a food product. Please scan a food item."
                } else {
                    self.errorMessage = "Classification failed: \(error.localizedDescription)"
                }
            }
        }
    }

    private func calculateSustainabilityScore(from classifications: [ClassificationResult]) {
        // Calculate a sustainability score based on the product classifications
        var score: Double = 50.0 // Start with a neutral score

        // Check for keywords in the classifications that indicate sustainability
        for classification in classifications {
            let label = classification.label.lowercased()
            let confidence = classification.confidence

            // Adjust score based on classification labels and confidence
            if label.contains("organic") {
                score += 15.0 * confidence
            }

            if label.contains("local") {
                score += 20.0 * confidence
            }

            if label.contains("imported") {
                score -= 15.0 * confidence
            }

            if label.contains("processed") {
                score -= 10.0 * confidence
            }

            if label.contains("packaged") {
                score -= 5.0 * confidence
            }
        }

        // Ensure score is between 0 and 100
        sustainabilityScore = max(0.0, min(100.0, score))
    }

    private func confidenceColor(for confidence: Double) -> Color {
        if confidence >= 0.7 {
            return .green
        } else if confidence >= 0.4 {
            return .yellow
        } else {
            return .orange
        }
    }

    private func healthRatingColor(for rating: String) -> Color {
        switch rating {
        case "A":
            return .green
        case "B":
            return .blue
        case "C":
            return .yellow
        case "D":
            return .orange
        case "E":
            return .red
        default:
            return .gray
        }
    }

    private func nutritionScoreColor(for score: Int) -> Color {
        if score >= 80 {
            return .green
        } else if score >= 60 {
            return .blue
        } else if score >= 40 {
            return .yellow
        } else if score >= 20 {
            return .orange
        } else {
            return .red
        }
    }

    private func sustainabilityColor(for score: Double) -> Color {
        if score >= 80 {
            return .green
        } else if score >= 60 {
            return .blue
        } else if score >= 40 {
            return .yellow
        } else if score >= 20 {
            return .orange
        } else {
            return .red
        }
    }

    private func sustainabilityRating(for score: Double) -> String {
        if score >= 80 {
            return "Excellent"
        } else if score >= 60 {
            return "Good"
        } else if score >= 40 {
            return "Average"
        } else if score >= 20 {
            return "Poor"
        } else {
            return "Very Poor"
        }
    }

    private func sustainabilityFactors(for score: Double) -> [String] {
        if score >= 80 {
            return [
                "This product appears to be locally produced",
                "Minimal transportation impact",
                "Likely uses sustainable packaging",
                "Probably organic or sustainably farmed"
            ]
        } else if score >= 60 {
            return [
                "This product may be regionally produced",
                "Moderate transportation impact",
                "Uses partially sustainable packaging",
                "May use some sustainable farming practices"
            ]
        } else if score >= 40 {
            return [
                "This product is likely produced domestically",
                "Significant transportation impact",
                "Uses conventional packaging",
                "Conventional farming methods likely used"
            ]
        } else {
            return [
                "This product appears to be imported",
                "High transportation impact",
                "Uses non-recyclable packaging",
                "Conventional farming methods likely used"
            ]
        }
    }

    private func sustainabilityRecommendations(for score: Double) -> [String] {
        if score >= 80 {
            return [
                "Continue choosing local, sustainable products",
                "Consider reusable containers for storage"
            ]
        } else if score >= 60 {
            return [
                "Look for more locally produced alternatives",
                "Check for products with more sustainable packaging"
            ]
        } else if score >= 40 {
            return [
                "Consider local alternatives to this product",
                "Look for products with less packaging",
                "Check farmers markets for similar items"
            ]
        } else {
            return [
                "Try to find local alternatives to this product",
                "Consider making this product at home if possible",
                "Look for products with recyclable packaging",
                "Check seasonal availability of local alternatives"
            ]
        }
    }
}

// Image picker component
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var image: UIImage?
    @Environment(\.presentationMode) var presentationMode

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.allowsEditing = true
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker

        init(_ parent: ImagePicker) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let editedImage = info[.editedImage] as? UIImage {
                parent.image = editedImage
            } else if let originalImage = info[.originalImage] as? UIImage {
                parent.image = originalImage
            }

            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}
