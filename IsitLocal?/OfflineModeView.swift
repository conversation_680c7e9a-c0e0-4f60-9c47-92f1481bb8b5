//
//  OfflineModeView.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-02.
//

import SwiftUI
import CoreData

struct OfflineModeView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @State private var offlineProducts: [OfflineProduct] = []
    @State private var isLoading = true
    
    private var offlineManager: OfflineManager {
        return OfflineManager(context: viewContext)
    }
    
    var body: some View {
        NavigationView {
            VStack {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                        .scaleEffect(1.5)
                        .padding()
                } else if offlineProducts.isEmpty {
                    Text("No offline products saved")
                        .font(.headline)
                        .foregroundColor(.gray)
                        .padding()
                } else {
                    List {
                        ForEach(offlineProducts, id: \.barcode) { product in
                            VStack(alignment: .leading, spacing: 5) {
                                Text(product.productName ?? "Unknown Product")
                                    .font(.headline)
                                Text("Origin: \(product.origin ?? "Unknown")")
                                    .font(.subheadline)
                                Text("Barcode: \(product.barcode ?? "Unknown")")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                if let savedDate = product.savedDate {
                                    Text("Saved: \(savedDate, formatter: itemFormatter)")
                                        .font(.caption)
                                        .foregroundColor(.gray)
                                }
                            }
                            .padding(.vertical, 5)
                        }
                        .onDelete(perform: deleteOfflineProducts)
                    }
                }
            }
            .navigationTitle("Offline Products")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    EditButton()
                }
            }
            .onAppear {
                loadOfflineProducts()
            }
        }
    }
    
    private func loadOfflineProducts() {
        isLoading = true
        
        DispatchQueue.main.async {
            self.offlineProducts = self.offlineManager.getOfflineProducts()
            self.isLoading = false
        }
    }
    
    private func deleteOfflineProducts(at offsets: IndexSet) {
        for index in offsets {
            let product = offlineProducts[index]
            offlineManager.deleteOfflineProduct(product: product)
        }
        
        loadOfflineProducts()
    }
}

private let itemFormatter: DateFormatter = {
    let formatter = DateFormatter()
    formatter.dateStyle = .medium
    formatter.timeStyle = .short
    return formatter
}()
