//
//  AdvancedDataProcessing.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-08.
//

import Foundation
import CoreData
import Combine

class AdvancedDataProcessing {
    // Singleton instance
    static let shared = AdvancedDataProcessing()

    // Publishers for data updates
    private let productSubject = PassthroughSubject<ProductCache, Never>()
    private let sustainabilitySubject = PassthroughSubject<SustainabilityInfo, Never>()

    // Publishers that can be observed by views
    var productPublisher: AnyPublisher<ProductCache, Never> {
        return productSubject.eraseToAnyPublisher()
    }

    var sustainabilityPublisher: AnyPublisher<SustainabilityInfo, Never> {
        return sustainabilitySubject.eraseToAnyPublisher()
    }

    private init() {
        // Initialize any resources needed
    }

    // Process product data with advanced algorithms
    func processProductData(barcode: String, context: NSManagedObjectContext) async -> ProductCache? {
        print("Advanced processing for barcode: \(barcode)")

        // Check if product exists in cache
        let fetchRequest: NSFetchRequest<ProductCache> = ProductCache.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "barcode == %@", barcode)
        fetchRequest.fetchLimit = 1

        do {
            let results = try context.fetch(fetchRequest)
            if let existingProduct = results.first {
                // Check if data is fresh (less than 7 days old)
                if let lastUpdated = existingProduct.lastUpdated,
                   Date().timeIntervalSince(lastUpdated) < 7 * 24 * 60 * 60 {
                    // Data is fresh, return it
                    return existingProduct
                }

                // Data is stale, update it
                return await updateProductData(existingProduct: existingProduct, context: context)
            } else {
                // Product doesn't exist, fetch new data
                return await fetchNewProductData(barcode: barcode, context: context)
            }
        } catch {
            print("Error fetching product from cache: \(error)")
            return nil
        }
    }

    // Update existing product data
    private func updateProductData(existingProduct: ProductCache, context: NSManagedObjectContext) async -> ProductCache {
        guard let barcode = existingProduct.barcode else {
            return existingProduct
        }

        // Use ProductDataManager to fetch updated data
        let dataManager = ProductDataManager()

        do {
            let (name, manufacturer, ingredients, category, origin) = try await dataManager.lookupProduct(code: barcode, cache: nil)

            // Update the existing product with new data
            if let name = name {
                existingProduct.productName = name
            }

            if let manufacturer = manufacturer {
                existingProduct.manufacturer = manufacturer
            }

            if let ingredients = ingredients {
                existingProduct.ingredients = ingredients
            }

            if let category = category {
                existingProduct.category = category
            }

            if let origin = origin {
                existingProduct.origin = origin
            }

            existingProduct.lastUpdated = Date()

            try context.save()

            // Notify subscribers
            productSubject.send(existingProduct)

            return existingProduct
        } catch {
            print("Error updating product data: \(error)")
            return existingProduct
        }
    }

    // Fetch new product data
    private func fetchNewProductData(barcode: String, context: NSManagedObjectContext) async -> ProductCache? {
        // Use ProductDataManager to fetch data
        let dataManager = ProductDataManager()

        do {
            let (name, manufacturer, ingredients, category, origin) = try await dataManager.lookupProduct(code: barcode, cache: nil)

            // Create new product cache
            let newProduct = ProductCache(context: context)
            newProduct.barcode = barcode
            newProduct.productName = name
            newProduct.manufacturer = manufacturer
            newProduct.ingredients = ingredients
            newProduct.category = category
            newProduct.origin = origin
            newProduct.lastUpdated = Date()

            try context.save()

            // Notify subscribers
            productSubject.send(newProduct)

            return newProduct
        } catch {
            print("Error fetching new product data: \(error)")
            return nil
        }
    }

    // Process sustainability data with advanced algorithms
    func processSustainabilityData(barcode: String, origin: String, productType: String, context: NSManagedObjectContext) async -> SustainabilityInfo? {
        print("Advanced sustainability processing for barcode: \(barcode)")

        // Check if sustainability info exists
        let fetchRequest: NSFetchRequest<SustainabilityInfo> = SustainabilityInfo.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "barcode == %@", barcode)
        fetchRequest.fetchLimit = 1

        do {
            let results = try context.fetch(fetchRequest)
            if let existingInfo = results.first {
                // Check if data is fresh (less than 30 days old)
                if let calculationDate = existingInfo.calculationDate,
                   Date().timeIntervalSince(calculationDate) < 30 * 24 * 60 * 60 {
                    // Data is fresh, return it
                    return existingInfo
                }

                // Data is stale, update it
                return await updateSustainabilityData(existingInfo: existingInfo, origin: origin, productType: productType, context: context)
            } else {
                // Sustainability info doesn't exist, create new
                return await createNewSustainabilityData(barcode: barcode, origin: origin, productType: productType, context: context)
            }
        } catch {
            print("Error fetching sustainability info: \(error)")
            return nil
        }
    }

    // Update existing sustainability data
    private func updateSustainabilityData(existingInfo: SustainabilityInfo, origin: String, productType: String, context: NSManagedObjectContext) async -> SustainabilityInfo {
        // Use SustainabilityManager to calculate updated scores
        let sustainabilityManager = SustainabilityManager(context: context)

        // Get updated sustainability info
        let updatedInfo = sustainabilityManager.calculateSustainabilityScore(barcode: existingInfo.barcode ?? "", origin: origin, productType: productType)

        // Notify subscribers
        sustainabilitySubject.send(updatedInfo)

        return updatedInfo
    }

    // Create new sustainability data
    private func createNewSustainabilityData(barcode: String, origin: String, productType: String, context: NSManagedObjectContext) async -> SustainabilityInfo {
        // Use SustainabilityManager to calculate scores
        let sustainabilityManager = SustainabilityManager(context: context)

        // Get new sustainability info
        let newInfo = sustainabilityManager.calculateSustainabilityScore(barcode: barcode, origin: origin, productType: productType)

        // Notify subscribers
        sustainabilitySubject.send(newInfo)

        return newInfo
    }

    // Batch process multiple products
    func batchProcessProducts(barcodes: [String], context: NSManagedObjectContext) async -> [ProductCache] {
        var results: [ProductCache] = []

        // Process each barcode concurrently
        await withTaskGroup(of: ProductCache?.self) { group in
            for barcode in barcodes {
                group.addTask {
                    return await self.processProductData(barcode: barcode, context: context)
                }
            }

            // Collect results
            for await result in group {
                if let product = result {
                    results.append(product)
                }
            }
        }

        return results
    }

    // Generate insights from user scan history
    func generateUserInsights(context: NSManagedObjectContext) -> UserInsights {
        // Fetch all scanned items
        let fetchRequest: NSFetchRequest<Item> = Item.fetchRequest()

        do {
            let items = try context.fetch(fetchRequest)

            // Calculate insights
            let totalItems = items.count
            var localItems = 0
            var countryCounts: [String: Int] = [:]
            var categoryCounts: [String: Int] = [:]

            for item in items {
                // Count local items
                if let origin = item.origin {
                    // Use a default country instead of creating a new LocationManager instance
                    let userCountry = "United States"
                    if origin.contains(userCountry) {
                        localItems += 1
                    }

                    // Count by country
                    countryCounts[origin, default: 0] += 1
                }

                // Count by category
                if let category = item.category {
                    categoryCounts[category, default: 0] += 1
                }
            }

            // Calculate local percentage
            let localPercentage = totalItems > 0 ? Double(localItems) / Double(totalItems) * 100 : 0

            // Find most common origin
            let mostCommonOrigin = countryCounts.max(by: { $0.value < $1.value })?.key ?? "Unknown"

            // Find most common category
            let mostCommonCategory = categoryCounts.max(by: { $0.value < $1.value })?.key ?? "Unknown"

            // Create insights object
            return UserInsights(
                totalScannedItems: totalItems,
                localItemsPercentage: localPercentage,
                mostCommonOrigin: mostCommonOrigin,
                mostCommonCategory: mostCommonCategory,
                countryCounts: countryCounts,
                categoryCounts: categoryCounts
            )
        } catch {
            print("Error generating user insights: \(error)")
            return UserInsights(
                totalScannedItems: 0,
                localItemsPercentage: 0,
                mostCommonOrigin: "Unknown",
                mostCommonCategory: "Unknown",
                countryCounts: [:],
                categoryCounts: [:]
            )
        }
    }
}

// Model for user insights
struct UserInsights {
    let totalScannedItems: Int
    let localItemsPercentage: Double
    let mostCommonOrigin: String
    let mostCommonCategory: String
    let countryCounts: [String: Int]
    let categoryCounts: [String: Int]
}
