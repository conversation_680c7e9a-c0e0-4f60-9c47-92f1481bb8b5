//
//  ContentView.swift
//  IsitLocal
//
//  Created by <PERSON> on 2025-04-02.
//

import SwiftUI
import CoreData
import AVFoundation

#if canImport(UIKit)
import UIKit
#endif

// Import AppState for state management
// Import SimpleScannerView for barcode scanning

struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Item.timestamp, ascending: false)],
        animation: .default)
    private var items: FetchedResults<Item>
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \ProductCache.lastUpdated, ascending: false)],
        predicate: nil,
        animation: .default)
    private var productCache: FetchedResults<ProductCache>
    @EnvironmentObject private var appState: AppState
    @State private var selectedTab: Int = 0

    var body: some View {
        // Just return the ScanView directly
        ScanView()
        // No TabView here since it's handled by EnhancedContentView
    }
}

struct ScanView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject private var appState: AppState
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Item.timestamp, ascending: false)],
        animation: .default)
    private var items: FetchedResults<Item>
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \ProductCache.lastUpdated, ascending: false)],
        predicate: nil,
        animation: .default)
    private var productCache: FetchedResults<ProductCache>

    @State private var scannedCode: String? = nil
    @State private var isScanning: Bool = false
    @State private var productName: String? = nil
    @State private var manufacturer: String? = nil
    @State private var ingredients: String? = nil
    @State private var category: String? = nil
    @State private var origin: String? = nil
    @State private var productImageUrl: String? = nil
    @State private var errorMessage: String? = nil
    @State private var showingHistory: Bool = false
    @State private var isLoading: Bool = false
    @State private var showingShareSheet: Bool = false
    @State private var shareText: String = ""
    @State private var filterCountry: String? = nil
    @State private var filterDate: Date? = nil
    @State private var showingNotesEditor: Bool = false
    @State private var notes: String = ""
    @State private var userCountry: String? = nil
    @State private var priceComparisons: [(retailer: String, title: String, price: String, url: String, available: Bool)] = []
    @State private var showLocationAlert: Bool = false
    @State private var showingDetailView: Bool = false
    @State private var showingGeminiConfig: Bool = false
    @StateObject private var locationManager = LocationManager()
    private let dataManager = ProductDataManager()
    private let priceComparisonManager = PriceComparisonManager()
    // Use app state for initialization

    init() {
        // Initialize ScanView
    }

    // Function to check the cache for a barcode
    private func checkCache(for barcode: String) -> ProductCache? {
        print("Checking cache for barcode: \(barcode)")
        let result = productCache.first { $0.barcode == barcode }
        print("Cache result: \(result != nil ? "Found" : "Not found")")
        return result
    }

    // Function to save a product to the cache
    private func saveToCache(barcode: String, productName: String?, manufacturer: String?, ingredients: String?, category: String?, origin: String?, imageUrl: String? = nil) {
        let newCache = ProductCache(context: viewContext)
        newCache.barcode = barcode
        newCache.productName = productName
        newCache.manufacturer = manufacturer
        newCache.ingredients = ingredients
        newCache.category = category
        newCache.origin = origin
        newCache.imageUrl = imageUrl
        newCache.lastUpdated = Date()

        do {
            try viewContext.save()
            // Successfully saved to cache
        } catch {
            print("Error saving to cache: \(error.localizedDescription)")
        }
    }

    // Function to save the scan to Core Data
    private func saveScan(code: String, productName: String?, origin: String?) {
        let newItem = Item(context: viewContext)
        newItem.timestamp = Date()
        newItem.barcode = code
        newItem.productName = productName ?? "Unknown Product"
        newItem.origin = origin
        newItem.isFavorite = false
        newItem.category = category ?? "Uncategorized"
        newItem.notes = notes.isEmpty ? nil : notes

        do {
            try viewContext.save()
            // Successfully saved scan to Core Data
        } catch {
            print("Error saving scan to Core Data: \(error.localizedDescription)")
        }
    }

    // Process a scanned barcode
    private func processScannedCode(_ code: String) {
        print("Processing scanned code: \(code)")

        // Set state variables
        DispatchQueue.main.async {
            self.scannedCode = code
            self.isScanning = false
            self.isLoading = true
        }

        Task {
            do {
                // First check if we have this product in cache
                if let cachedProduct = checkCache(for: code) {
                    // Update UI on main thread
                    DispatchQueue.main.async {
                        self.productName = cachedProduct.productName
                        self.manufacturer = cachedProduct.manufacturer
                        self.ingredients = cachedProduct.ingredients
                        self.category = cachedProduct.category
                        self.origin = cachedProduct.origin
                        self.productImageUrl = cachedProduct.imageUrl
                        self.errorMessage = nil

                        // Prepare share text
                        self.shareText = "\(cachedProduct.productName ?? "Unknown Product") is from \(cachedProduct.origin ?? "Unknown")."
                    }

                    // Save the scan to Core Data
                    saveScan(code: code, productName: cachedProduct.productName, origin: cachedProduct.origin)

                    // Fetch price comparisons if product name is available
                    if let productName = cachedProduct.productName, let userCountry = userCountry {
                        await fetchPriceComparisons(productName: productName, barcode: code, country: userCountry)
                    }

                    // Set loading to false on main thread
                    DispatchQueue.main.async {
                        self.isLoading = false
                    }
                    return
                }

                // If not in cache, look up the product
                print("Looking up product data for code: \(code)")
                let (name, manufacturer, ingredients, category, origin, imageUrl) = try await dataManager.lookupProduct(code: code, cache: nil)

                // Update UI on main thread
                DispatchQueue.main.async {
                    self.productName = name
                    self.manufacturer = manufacturer
                    self.ingredients = ingredients
                    self.category = category
                    self.origin = origin
                    self.productImageUrl = imageUrl
                    self.errorMessage = nil
                }

                // Save the scan to Core Data
                saveScan(code: code, productName: name, origin: origin)

                // Prepare share text
                DispatchQueue.main.async {
                    self.shareText = "\(name ?? "Unknown Product") is from \(origin ?? "Unknown")."
                }

                // Validate and enhance data with Gemini API
                Task {
                    do {
                        let validation = try await GeminiAPIManager.shared.validateAndEnhanceProductData(
                            productName: name,
                            manufacturer: manufacturer,
                            barcode: code,
                            origin: origin,
                            category: category
                        )

                        print("Gemini validation - Confidence: \(validation.confidence)/10")
                        if let suggestions = validation.suggestions {
                            print("Gemini suggestions: \(suggestions)")
                        }

                        // Find local alternatives
                        if let productName = name, let category = category {
                            let alternatives = try await GeminiAPIManager.shared.findLocalAlternatives(
                                productName: productName,
                                category: category,
                                userLocation: userCountry ?? "Canada"
                            )

                            if let localBrands = alternatives.localBrands {
                                print("Local alternatives: \(localBrands)")
                            }
                        }

                    } catch {
                        print("Gemini enhancement failed: \(error.localizedDescription)")
                    }
                }

                // Save to cache with image URL
                saveToCache(barcode: code, productName: name, manufacturer: manufacturer, ingredients: ingredients, category: category, origin: origin, imageUrl: imageUrl)

                // Fetch price comparisons if product name is available
                if let productName = name, let userCountry = userCountry {
                    await fetchPriceComparisons(productName: productName, barcode: code, country: userCountry)
                }
            }

            // Note: isLoading is already set to false in the catch block if there's an error
        }
    }

    // Function to fetch price comparisons
    private func fetchPriceComparisons(productName: String, barcode: String, country: String) async {
        // Fetch price comparisons from the manager
        let comparisons = await priceComparisonManager.fetchPriceComparisons(productName: productName, barcode: barcode, country: country)
        self.priceComparisons = comparisons
    }

    var body: some View {
        // Use NavigationView for scan interface
        NavigationView {
            ZStack {
                // Background gradient
                LinearGradient(gradient: Gradient(colors: [.blue.opacity(0.3), .green.opacity(0.3)]), startPoint: .top, endPoint: .bottom)
                    .ignoresSafeArea()

                VStack(spacing: 20) {
                    // App title
                    Text("Is It Local?")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .padding(.top, 40)
                        .shadow(radius: 2)

                    if isScanning {
                        // Scanner container with fixed height
                        ZStack {
                            Color.black.opacity(0.1) // Background for scanner

                            // Scanner view
                            SimpleScannerView { result in
                                do {
                                    switch result {
                                    case .success(let code):
                                        // Process valid scanned code
                                        print("Successfully scanned code: \(code)")
                                        processScannedCode(code)
                                    case .failure(let error):
                                        // Handle scanning failure
                                        self.isScanning = false
                                        self.errorMessage = "Scanning failed: \(error.localizedDescription)"
                                        print("Scanning error: \(error.localizedDescription)")
                                    }
                                } catch {
                                    // Handle any unexpected errors
                                    self.isScanning = false
                                    self.errorMessage = "Unexpected error: \(error.localizedDescription)"
                                    print("Unexpected error: \(error.localizedDescription)")
                                }
                            }

                            // Scanning indicator
                            VStack {
                                Spacer()
                                Text("Scanning...")
                                    .font(.caption)
                                    .foregroundColor(.white)
                                    .padding(8)
                                    .background(Color.black.opacity(0.6))
                                    .cornerRadius(8)
                            }
                            .padding(.bottom, 10)
                        }
                        .frame(height: 300)
                        .cornerRadius(15)
                        .overlay(
                            RoundedRectangle(cornerRadius: 15)
                                .stroke(Color.white, lineWidth: 2)
                        )
                        .padding(.horizontal)
                    } else {
                        // Results card
                        VStack(spacing: 10) {
                            if isLoading {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                                    .scaleEffect(1.5)
                                    .padding()
                            }
                            if let errorMessage = errorMessage {
                                Text(errorMessage)
                                    .font(.headline)
                                    .foregroundColor(.red)
                                    .padding(.bottom, 5)
                            }

                            if let productName = productName {
                                // Product found - show enhanced detailed card
                                VStack(spacing: 8) {
                                    // Enhanced product image
                                    if let imageUrl = productImageUrl {
                                        RemoteImageView(url: URL(string: imageUrl), width: 120, height: 120)
                                        .padding(.bottom, 2)
                                    } else {
                                        Image(systemName: "photo")
                                            .resizable()
                                            .aspectRatio(contentMode: .fit)
                                            .frame(width: 120, height: 120)
                                            .foregroundColor(.gray)
                                            .padding(.bottom, 2)
                                    }

                                    // Enhanced product name
                                    Text(productName)
                                        .font(.headline)
                                        .fontWeight(.bold)
                                        .foregroundColor(.black)
                                        .multilineTextAlignment(.center)
                                        .padding(.horizontal, 4)
                                        .padding(.bottom, 2)

                                    // Enhanced origin with icon
                                    if let origin = origin {
                                        HStack {
                                            Image(systemName: "globe")
                                                .foregroundColor(.blue)
                                                .font(.subheadline)
                                            Text("Origin: \(origin)")
                                                .font(.subheadline)
                                                .fontWeight(.semibold)
                                                .foregroundColor(.black)
                                        }
                                        .padding(.vertical, 4)
                                        .padding(.horizontal, 12)
                                        .background(Color.green.opacity(0.2))
                                        .clipShape(RoundedRectangle(cornerRadius: 8))
                                    }

                                    Divider()
                                        .padding(.vertical, 2)

                                    // Enhanced product details with icons - more compact
                                    VStack(alignment: .leading, spacing: 6) {
                                        if let scannedCode = scannedCode {
                                            HStack(alignment: .center) {
                                                Image(systemName: "barcode")
                                                    .foregroundColor(.blue)
                                                    .font(.caption)
                                                    .frame(width: 16)
                                                Text("Barcode:")
                                                    .font(.caption)
                                                    .foregroundColor(.gray)
                                                    .frame(width: 80, alignment: .leading)
                                                Text(scannedCode)
                                                    .font(.caption)
                                                    .foregroundColor(.black)
                                            }
                                        }

                                        if let manufacturer = manufacturer {
                                            HStack(alignment: .center) {
                                                Image(systemName: "building.2")
                                                    .foregroundColor(.blue)
                                                    .font(.caption)
                                                    .frame(width: 16)
                                                Text("Manufacturer:")
                                                    .font(.caption)
                                                    .foregroundColor(.gray)
                                                    .frame(width: 80, alignment: .leading)
                                                Text(manufacturer)
                                                    .font(.caption)
                                                    .foregroundColor(.black)
                                                    .multilineTextAlignment(.trailing)
                                            }
                                        }

                                        if let category = category {
                                            HStack(alignment: .center) {
                                                Image(systemName: "tag")
                                                    .foregroundColor(.blue)
                                                    .font(.caption)
                                                    .frame(width: 16)
                                                Text("Category:")
                                                    .font(.caption)
                                                    .foregroundColor(.gray)
                                                    .frame(width: 80, alignment: .leading)
                                                Text(category)
                                                    .font(.caption)
                                                    .foregroundColor(.black)
                                                    .multilineTextAlignment(.trailing)
                                            }
                                        }
                                    }
                                    .padding(.horizontal, 15)

                                    if let ingredients = ingredients, !ingredients.isEmpty {
                                        Divider()
                                            .padding(.vertical, 4)

                                        VStack(alignment: .leading, spacing: 4) {
                                            HStack {
                                                Image(systemName: "list.bullet")
                                                    .foregroundColor(.blue)
                                                    .font(.caption)
                                                Text("Ingredients:")
                                                    .font(.caption)
                                                    .foregroundColor(.gray)
                                            }

                                            Text(ingredients)
                                                .font(.caption2)
                                                .foregroundColor(.black)
                                                .fixedSize(horizontal: false, vertical: true)
                                                .padding(.top, 2)
                                        }
                                        .padding(.horizontal, 10)
                                    }

                                    // View more details button
                                    NavigationLink {
                                        ProductDetailView(
                                            barcode: scannedCode ?? "",
                                            productName: productName,
                                            manufacturer: manufacturer,
                                            ingredients: ingredients,
                                            category: category,
                                            origin: origin,
                                            imageUrl: productImageUrl
                                        )
                                    } label: {
                                        Text("View More Details")
                                            .font(.headline)
                                            .foregroundColor(.blue)
                                            .padding(.vertical, 8)
                                    }
                                    .padding(.top, 5)
                                }
                            } else if let origin = origin {
                                // Only origin found - show simplified card
                                VStack(spacing: 10) {
                                    Image(systemName: "globe")
                                        .resizable()
                                        .aspectRatio(contentMode: .fit)
                                        .frame(width: 80, height: 80)
                                        .foregroundColor(.blue)
                                        .padding(.bottom, 5)

                                    if let scannedCode = scannedCode {
                                        Text("Barcode: \(scannedCode)")
                                            .font(.subheadline)
                                            .foregroundColor(.gray)
                                    }

                                    Text("Origin: \(origin)")
                                        .font(.title3)
                                        .fontWeight(.medium)
                                        .foregroundColor(.black)
                                        .multilineTextAlignment(.center)
                                        .padding(.horizontal)
                                }
                            } else {
                                // Nothing scanned yet
                                VStack(spacing: 15) {
                                    Image(systemName: "barcode.viewfinder")
                                        .resizable()
                                        .aspectRatio(contentMode: .fit)
                                        .frame(width: 80, height: 80)
                                        .foregroundColor(.gray)

                                    Text("Scan a barcode or QR code to check its origin")
                                        .font(.title3)
                                        .fontWeight(.medium)
                                        .foregroundColor(.black)
                                        .multilineTextAlignment(.center)
                                        .padding(.horizontal)
                                }
                                .padding(.vertical, 20)
                            }
                            if !priceComparisons.isEmpty {
                                VStack(alignment: .leading, spacing: 3) {
                                    Text("Price Comparisons:")
                                        .font(.caption)
                                        .fontWeight(.bold)
                                        .foregroundColor(.black)
                                    ForEach(priceComparisons, id: \.url) { comparison in
                                        VStack(alignment: .leading, spacing: 1) {
                                            Text("\(comparison.retailer): \(comparison.price)")
                                                .font(.caption)
                                                .foregroundColor(.black)
                                            Link(comparison.title, destination: URL(string: comparison.url)!)
                                                .font(.caption2)
                                                .foregroundColor(.blue)
                                            Text(comparison.available ? "In Stock" : "Out of Stock")
                                                .font(.caption2)
                                                .foregroundColor(comparison.available ? .green : .red)
                                        }
                                    }
                                }
                                .padding(.top, 5)
                            } else if productName != nil {
                                Text("No price comparisons available")
                                    .font(.subheadline)
                                    .foregroundColor(.gray)
                                    .padding(.top, 5)
                            }
                            if productName != nil || origin != nil {
                                // Action buttons in a horizontal grid - more compact
                                VStack(spacing: 6) {
                                    // Share button
                                    Button(action: {
                                        showingShareSheet = true
                                    }) {
                                        HStack {
                                            Image(systemName: "square.and.arrow.up")
                                                .font(.caption)
                                            Text("Share Result")
                                                .font(.caption)
                                        }
                                        .padding(.vertical, 8)
                                        .padding(.horizontal, 10)
                                        .frame(maxWidth: .infinity)
                                        .background(Color.green)
                                        .foregroundColor(.white)
                                        .cornerRadius(8)
                                        .shadow(radius: 1)
                                    }
                                    .padding(.horizontal, 8)
                                    .sheet(isPresented: $showingShareSheet) {
                                        ShareSheet(activityItems: [shareText])
                                    }

                                    // Dismiss button
                                    Button(action: {
                                        // Reset all product data
                                        DispatchQueue.main.async {
                                            self.scannedCode = nil
                                            self.productName = nil
                                            self.manufacturer = nil
                                            self.ingredients = nil
                                            self.category = nil
                                            self.origin = nil
                                            self.productImageUrl = nil
                                            self.errorMessage = nil
                                            self.priceComparisons = []
                                        }
                                    }) {
                                        HStack {
                                            Image(systemName: "xmark.circle")
                                                .font(.caption)
                                            Text("Dismiss")
                                                .font(.caption)
                                        }
                                        .padding(.vertical, 8)
                                        .padding(.horizontal, 10)
                                        .frame(maxWidth: .infinity)
                                        .background(Color.gray.opacity(0.2))
                                        .foregroundColor(.black)
                                        .cornerRadius(8)
                                    }
                                    .padding(.horizontal, 8)
                                }
                            }
                            if !items.isEmpty, let lastItem = items.first(where: { $0.barcode == scannedCode }) {
                                // Favorites and Notes buttons in a horizontal grid - more compact
                                HStack(spacing: 8) {
                                    // Favorites button
                                    Button(action: {
                                        lastItem.isFavorite.toggle()
                                        do {
                                            try viewContext.save()
                                            // Successfully toggled favorite status
                                        } catch {
                                            print("Error toggling favorite status: \(error.localizedDescription)")
                                        }
                                    }) {
                                        HStack {
                                            Image(systemName: lastItem.isFavorite ? "star.fill" : "star")
                                                .font(.caption)
                                            Text(lastItem.isFavorite ? "Remove from Favorites" : "Add to Favorites")
                                                .font(.caption)
                                        }
                                        .padding(.vertical, 8)
                                        .padding(.horizontal, 10)
                                        .frame(maxWidth: .infinity)
                                        .background(lastItem.isFavorite ? Color.yellow : Color.gray.opacity(0.2))
                                        .foregroundColor(.black)
                                        .cornerRadius(8)
                                        .shadow(radius: 1)
                                    }

                                    // Notes button
                                    Button(action: {
                                        notes = lastItem.notes ?? ""
                                        showingNotesEditor = true
                                    }) {
                                        HStack {
                                            Image(systemName: "note.text")
                                                .font(.caption)
                                            Text("Add/Edit Notes")
                                                .font(.caption)
                                        }
                                        .padding(.vertical, 8)
                                        .padding(.horizontal, 10)
                                        .frame(maxWidth: .infinity)
                                        .background(Color.blue.opacity(0.2))
                                        .foregroundColor(.black)
                                        .cornerRadius(8)
                                        .shadow(radius: 1)
                                    }
                                }
                                .padding(.horizontal, 8)
                                .sheet(isPresented: $showingNotesEditor) {
                                    NotesEditorView(notes: $notes, onSave: { newNotes in
                                        lastItem.notes = newNotes
                                        do {
                                            try viewContext.save()
                                            // Successfully saved notes
                                        } catch {
                                            print("Error saving notes: \(error.localizedDescription)")
                                        }
                                    })
                                }
                            }
                        }
                        .padding()
                        .background(Color.white.opacity(0.95))
                        .clipShape(RoundedRectangle(cornerRadius: 20))
                        .shadow(color: Color.black.opacity(0.3), radius: 8, x: 0, y: 3)
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                        )
                        .padding(.horizontal)
                    }

                    Spacer()

                    // Scan button
                    Button(action: {
                        self.isScanning = true
                        self.errorMessage = nil
                        self.productName = nil
                        self.manufacturer = nil
                        self.ingredients = nil
                        self.category = nil
                        self.origin = nil
                        self.notes = ""
                        self.priceComparisons = []
                    }) {
                        HStack {
                            Image(systemName: "barcode.viewfinder")
                                .font(.title2)
                            Text("Scan Product")
                                .font(.title2)
                                .fontWeight(.bold)
                        }
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(LinearGradient(gradient: Gradient(colors: [.blue, .purple]), startPoint: .leading, endPoint: .trailing))
                        .foregroundColor(.white)
                        .clipShape(Capsule())
                        .shadow(radius: 3)
                    }
                    .padding(.horizontal)
                    .padding(.bottom, 100) // Add extra bottom padding to stay above tab bar
                }
            }
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        showingHistory.toggle()
                    }) {
                        Image(systemName: "clock.arrow.circlepath")
                            .foregroundColor(.white)
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack {
                        // Gemini AI Configuration Button
                        Button(action: {
                            showingGeminiConfig.toggle()
                        }) {
                            Image(systemName: "brain.head.profile")
                                .foregroundColor(.orange)
                        }

                        NavigationLink {
                            StatisticsView()
                        } label: {
                            Image(systemName: "chart.bar.fill")
                                .foregroundColor(.white)
                        }
                    }
                }
            }
            .sheet(isPresented: $showingHistory) {
                ScanHistoryView(filterCountry: $filterCountry, filterDate: $filterDate)
                    .environment(\.managedObjectContext, viewContext)
            }
            .sheet(isPresented: $showingGeminiConfig) {
                GeminiConfigurationView()
            }
            .onAppear {
                // Request location immediately
                locationManager.requestLocation()

                // Load Gemini API key if available
                if let savedAPIKey = UserDefaults.standard.string(forKey: "GeminiAPIKey"),
                   !savedAPIKey.isEmpty && savedAPIKey != "YOUR_FREE_GEMINI_API_KEY_HERE" {
                    GeminiAPIManager.shared.setAPIKey(savedAPIKey)
                    print("Gemini API key loaded from UserDefaults")
                }
            }
            .onChange(of: locationManager.userCountry) { oldCountry, newCountry in
                userCountry = newCountry
            }
            .onChange(of: locationManager.hasLocationError) { oldValue, hasError in
                if hasError {
                    showLocationAlert = true
                }
            }
            .alert(isPresented: $showLocationAlert) {
                Alert(
                    title: Text("Location Access Denied"),
                    message: Text("Please enable location services in Settings to allow price comparisons based on your location. Alternatively, price comparisons will default to the United States."),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
    }
}

struct FavoritesView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Item.timestamp, ascending: false)],
        predicate: NSPredicate(format: "isFavorite == true"),
        animation: .default)
    private var favoriteItems: FetchedResults<Item>

    var body: some View {
        NavigationView {
            List {
                ForEach(favoriteItems) { item in
                    VStack(alignment: .leading) {
                        Text("Product: \(item.productName ?? "Unknown")")
                            .font(.headline)
                        Text("Origin: \(item.origin ?? "Unknown")")
                            .font(.subheadline)
                        Text("Barcode: \(item.barcode ?? "Unknown")")
                            .font(.subheadline)
                        if let category = item.category {
                            Text("Category: \(category)")
                                .font(.subheadline)
                                .foregroundColor(.gray)
                        }
                        if let notes = item.notes, !notes.isEmpty {
                            Text("Notes: \(notes)")
                                .font(.subheadline)
                                .foregroundColor(.gray)
                        }
                        Text("Scanned: \(item.timestamp!, formatter: itemFormatter)")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                    .padding(.vertical, 5)
                }
                .onDelete(perform: deleteItems)
            }
            .navigationTitle("Favorites")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    EditButton()
                }
            }
        }
    }

    private func deleteItems(offsets: IndexSet) {
        withAnimation {
            offsets.map { favoriteItems[$0] }.forEach(viewContext.delete)
            do {
                try viewContext.save()
                // Items deleted from favorites
            } catch {
                print("Error deleting items from favorites: \(error.localizedDescription)")
            }
        }
    }
}

// View to display scan history with filtering
struct ScanHistoryView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Item.timestamp, ascending: false)],
        animation: .default)
    private var items: FetchedResults<Item>
    @Binding var filterCountry: String?
    @Binding var filterDate: Date?
    @State private var showingFilterOptions: Bool = false

    var filteredItems: [Item] {
        items.filter { item in
            var matchesCountry = true
            var matchesDate = true

            if let filterCountry = filterCountry, let origin = item.origin {
                matchesCountry = origin.contains(filterCountry)
            }

            if let filterDate = filterDate, let timestamp = item.timestamp {
                let calendar = Calendar.current
                matchesDate = calendar.isDate(timestamp, inSameDayAs: filterDate)
            }

            return matchesCountry && matchesDate
        }
    }

    var body: some View {
        NavigationView {
            VStack {
                // Filter button
                Button(action: {
                    showingFilterOptions.toggle()
                }) {
                    HStack {
                        Image(systemName: "line.3.horizontal.decrease.circle")
                            .font(.title2)
                        Text("Filter History")
                            .font(.title2)
                            .fontWeight(.bold)
                    }
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.gray.opacity(0.2))
                    .foregroundColor(.black)
                    .clipShape(Capsule())
                }
                .padding(.horizontal)
                .sheet(isPresented: $showingFilterOptions) {
                    FilterView(filterCountry: $filterCountry, filterDate: $filterDate)
                }

                // Display filtered items
                List {
                    ForEach(filteredItems) { item in
                        VStack(alignment: .leading) {
                            Text("Product: \(item.productName ?? "Unknown")")
                                .font(.headline)
                            Text("Origin: \(item.origin ?? "Unknown")")
                                .font(.subheadline)
                            Text("Barcode: \(item.barcode ?? "Unknown")")
                                .font(.subheadline)
                            if let category = item.category {
                                Text("Category: \(category)")
                                    .font(.subheadline)
                                    .foregroundColor(.gray)
                            }
                            if let notes = item.notes, !notes.isEmpty {
                                Text("Notes: \(notes)")
                                    .font(.subheadline)
                                    .foregroundColor(.gray)
                            }
                            Text("Scanned: \(item.timestamp!, formatter: itemFormatter)")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                        .padding(.vertical, 5)
                    }
                    .onDelete(perform: deleteItems)
                }
                .navigationTitle("Scan History")
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        EditButton()
                    }
                }
            }
        }
    }

    private func deleteItems(offsets: IndexSet) {
        withAnimation {
            offsets.map { filteredItems[$0] }.forEach(viewContext.delete)
            do {
                try viewContext.save()
                // Items deleted from scan history
            } catch {
                print("Error deleting items from scan history: \(error.localizedDescription)")
            }
        }
    }
}

// View for filtering scan history
struct FilterView: View {
    @Binding var filterCountry: String?
    @Binding var filterDate: Date?
    @Environment(\.dismiss) var dismiss

    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Filter by Country")) {
                    TextField("Enter country (e.g., Canada)", text: Binding(
                        get: { filterCountry ?? "" },
                        set: { filterCountry = $0.isEmpty ? nil : $0 }
                    ))
                }
                Section(header: Text("Filter by Date")) {
                    DatePicker("Select Date", selection: Binding(
                        get: { filterDate ?? Date() },
                        set: { filterDate = $0 }
                    ), displayedComponents: .date)
                    Button("Clear Date Filter") {
                        filterDate = nil
                    }
                    .foregroundColor(.red)
                }
            }
            .navigationTitle("Filter Options")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// View for editing scan notes
struct NotesEditorView: View {
    @Binding var notes: String
    let onSave: (String) -> Void
    @Environment(\.dismiss) var dismiss

    var body: some View {
        NavigationView {
            Form {
                TextField("Add notes (e.g., Bought at Walmart)", text: $notes, axis: .vertical)
                    .lineLimit(5)
            }
            .navigationTitle("Edit Notes")
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        onSave(notes)
                        dismiss()
                    }
                }
            }
        }
    }
}

// Share sheet for sharing scan results
struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]

    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: activityItems, applicationActivities: nil)
        return controller
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

private let itemFormatter: DateFormatter = {
    let formatter = DateFormatter()
    formatter.dateStyle = .short
    formatter.timeStyle = .medium
    return formatter
}()

#Preview {
    ContentView().environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
