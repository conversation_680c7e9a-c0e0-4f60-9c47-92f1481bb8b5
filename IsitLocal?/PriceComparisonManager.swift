//
//  ProductComparisonManager.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-02.
//

import Foundation
import CoreData

class ProductComparisonManager {
    private let context: NSManagedObjectContext
    
    init(context: NSManagedObjectContext) {
        self.context = context
    }
    
    // Function to compare products by origin
    func compareProductsByOrigin(product1: String, product2: String) -> ProductComparison? {
        // This is a simplified version that returns sample data
        let comparison = ProductComparison(context: context)
        comparison.product1Name = product1
        comparison.product2Name = product2
        comparison.product1Origin = "United States 🇺🇸"
        comparison.product2Origin = "China 🇨🇳"
        comparison.transportImpact1 = 25.5
        comparison.transportImpact2 = 75.8
        comparison.comparisonDate = Date()
        
        do {
            try context.save()
            return comparison
        } catch {
            print("Failed to save product comparison: \(error)")
            return nil
        }
    }
    
    // Function to get all product comparisons
    func getProductComparisons() -> [ProductComparison] {
        let fetchRequest: NSFetchRequest<ProductComparison> = ProductComparison.fetchRequest()
        fetchRequest.sortDescriptors = [NSSortDescriptor(keyPath: \ProductComparison.comparisonDate, ascending: false)]
        
        do {
            return try context.fetch(fetchRequest)
        } catch {
            print("Failed to fetch product comparisons: \(error)")
            return []
        }
    }
    
    // Function to delete a product comparison
    func deleteProductComparison(comparison: ProductComparison) {
        context.delete(comparison)
        
        do {
            try context.save()
            print("Deleted product comparison")
        } catch {
            print("Failed to delete product comparison: \(error)")
        }
    }
}
