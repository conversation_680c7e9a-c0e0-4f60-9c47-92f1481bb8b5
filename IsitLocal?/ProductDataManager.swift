//
//  ProductDataManager.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-02.
//

import Foundation
import CoreLocation

// Custom enum for location errors that conforms to Equatable
enum LocationError: Equatable {
    case denied
    case disabled
    case unknown(String)
}

class ProductDataManager {
    // Function to get the country from the barcode prefix
    func getProductOrigin(code: String) -> String {
        guard code.count >= 3 else {
            return "Unknown (Invalid Code)"
        }
        guard let prefix = Int(code.prefix(3)) else {
            return "Unknown (Invalid Prefix)"
        }

        // Use the BarcodeOriginDatabase to get the origin
        return BarcodeOriginDatabase.getOriginFromPrefix(prefix)
    }

    // Function to fetch product data from Open Food Facts API (free)
    func lookupProductFromOpenFoodFacts(code: String) async throws -> (String?, String?, String?, String?, String?) {
        let urlString = "https://world.openfoodfacts.org/api/v0/product/\(code).json"
        guard let url = URL(string: urlString) else {
            throw URLError(.badURL)
        }

        let (data, response) = try await URLSession.shared.data(from: url)
        guard let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 else {
            throw URLError(.badServerResponse)
        }

        struct OpenFoodFactsResponse: Decodable {
            let status: Int
            let product: Product?

            struct Product: Decodable {
                let productName: String?
                let brands: String?
                let ingredients: String?
                let categories: String?
                let imageUrl: String?

                enum CodingKeys: String, CodingKey {
                    case productName = "product_name"
                    case brands
                    case ingredients = "ingredients_text"
                    case categories
                    case imageUrl = "image_url"
                }

                init(from decoder: Decoder) throws {
                    let container = try decoder.container(keyedBy: CodingKeys.self)
                    productName = try container.decodeIfPresent(String.self, forKey: .productName)
                    brands = try container.decodeIfPresent(String.self, forKey: .brands)
                    ingredients = try container.decodeIfPresent(String.self, forKey: .ingredients)
                    categories = try container.decodeIfPresent(String.self, forKey: .categories)
                    imageUrl = try container.decodeIfPresent(String.self, forKey: .imageUrl)
                }
            }

            init(from decoder: Decoder) throws {
                let container = try decoder.container(keyedBy: CodingKeys.self)
                status = try container.decodeIfPresent(Int.self, forKey: .status) ?? 0
                product = try container.decodeIfPresent(Product.self, forKey: .product)
            }

            enum CodingKeys: String, CodingKey {
                case status
                case product
            }
        }

        do {
            let decoder = JSONDecoder()
            let result = try decoder.decode(OpenFoodFactsResponse.self, from: data)

            if result.status == 1, let product = result.product {
                return (product.productName,
                        product.brands,
                        product.ingredients,
                        product.categories,
                        product.imageUrl)
            } else {
                return (nil, nil, nil, nil, nil)
            }
        } catch {
            throw error
        }
    }

    // Function to fetch product data from UPC Database API
    func lookupProductFromUPCDatabase(code: String) async throws -> (String?, String?, String?, String?, String?) {
        let urlString = "https://api.upcdatabase.org/product/\(code)"

        // Validate the URL format
        guard let url = URL(string: urlString) else {
            throw URLError(.badURL)
        }

        // Create a request with API key (using a placeholder key)
        var request = URLRequest(url: url)
        request.addValue("DEMO_KEY", forHTTPHeaderField: "Authorization")

        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            guard let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 else {
                throw URLError(.badServerResponse)
            }

            // Parse the JSON response
            struct UPCResponse: Decodable {
                let success: Bool
                let product: Product?

                struct Product: Decodable {
                    let title: String?
                    let brand: String?
                    let description: String?
                    let category: String?
                    let images: [String]?
                }
            }

            let decoder = JSONDecoder()
            let result = try decoder.decode(UPCResponse.self, from: data)

            if result.success, let product = result.product {
                return (product.title,
                        product.brand,
                        product.description,
                        product.category,
                        product.images?.first)
            } else {
                return (nil, nil, nil, nil, nil)
            }
        } catch {
            throw error
        }
    }

    // Function to fetch product data from Barcode Lookup API
    func lookupProductFromBarcodeLookup(code: String) async throws -> (String?, String?, String?, String?, String?) {
        // Note: In a real app, you would need to sign up for an API key
        // This is a simplified implementation
        let urlString = "https://api.barcodelookup.com/v3/products?barcode=\(code)&formatted=y&key=DEMO_KEY"

        // Validate the URL format even though we're not making a real API call in this demo
        if URL(string: urlString) == nil {
            throw URLError(.badURL)
        }

        // In a real implementation, you would make the API call and parse the response
        // For this demo, we'll simulate a response
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second delay to simulate network call

        // Generate detailed product data based on the barcode
        // Use the barcode to create deterministic but varied product information
        // We'll use the hash of the code for consistent but varied results
        let codeHash = abs(code.hash)

        // Product categories
        let productCategories = ["Cereal", "Snack", "Beverage", "Dairy", "Bakery", "Canned Goods", "Frozen Food",
                               "Produce", "Meat", "Seafood", "Condiments", "Pasta", "Rice", "Soup", "Dessert"]

        // Brands with country associations
        let brands = [
            "Nature's Best (USA)",
            "Healthy Choice (Canada)",
            "Organic Farms (UK)",
            "Green Valley (Australia)",
            "Pure Foods (New Zealand)",
            "Alpine Fresh (Switzerland)",
            "Nordic Naturals (Sweden)",
            "Mediterranean Delights (Italy)",
            "Sunshine Harvest (Spain)",
            "Eastern Spice (India)",
            "Rising Sun (Japan)",
            "Dragon Flavors (China)",
            "Tropical Taste (Brazil)",
            "Savanna Select (South Africa)",
            "Maple Grove (Canada)"
        ]

        // Product descriptors
        let descriptors = ["Organic", "Premium", "Natural", "Classic", "Artisanal", "Gourmet", "Traditional", "Homestyle", "Signature", "Select"]

        // Generate product details
        let categoryIndex = codeHash % productCategories.count
        let brandIndex = (codeHash / 10) % brands.count
        let descriptorIndex = (codeHash / 100) % descriptors.count

        let productType = productCategories[categoryIndex]
        let brand = brands[brandIndex]
        let descriptor = descriptors[descriptorIndex]

        // Create product name with variety
        let productName = "\(brand) \(descriptor) \(productType)"

        // Create varied ingredients list based on product type
        var ingredients = ""

        switch productType {
        case "Cereal":
            ingredients = "Whole Grain Wheat, Sugar, Rice, Corn Syrup, Salt, Malt Flavoring, Vitamins & Minerals"
        case "Snack":
            ingredients = "Corn, Vegetable Oil, Salt, Cheese Powder, Whey, Monosodium Glutamate, Natural Flavors"
        case "Beverage":
            ingredients = "Carbonated Water, High Fructose Corn Syrup, Caramel Color, Phosphoric Acid, Natural Flavors, Caffeine"
        case "Dairy":
            ingredients = "Milk, Cream, Sugar, Natural Flavors, Live Active Cultures, Vitamin D"
        case "Bakery":
            ingredients = "Wheat Flour, Water, Sugar, Yeast, Salt, Vegetable Oil, Eggs, Milk"
        case "Canned Goods":
            ingredients = "Tomatoes, Tomato Juice, Salt, Citric Acid, Calcium Chloride"
        case "Frozen Food":
            ingredients = "Pasta, Water, Cheese, Milk, Butter, Salt, Spices, Preservatives"
        case "Produce":
            ingredients = "100% Fresh Produce"
        case "Meat":
            ingredients = "Beef, Salt, Spices, Natural Flavors"
        case "Seafood":
            ingredients = "Salmon, Salt, Natural Flavors"
        default:
            ingredients = "Water, Sugar, Salt, Natural Flavors, Vitamins & Minerals"
        }

        return (productName, brand, ingredients, productType, nil)
    }

    // Combined lookup function with multiple sources and web scraping
    func lookupProduct(code: String, cache: ProductCache?) async throws -> (String?, String?, String?, String?, String?) {
        // Check the cache first
        if let cachedProduct = cache {
            // Verify the cached origin against the barcode prefix
            let barcodeOrigin = getProductOrigin(code: code)
            if cachedProduct.origin != barcodeOrigin {
                return (cachedProduct.productName, cachedProduct.manufacturer, cachedProduct.ingredients, cachedProduct.category, barcodeOrigin)
            }
            return (cachedProduct.productName, cachedProduct.manufacturer, cachedProduct.ingredients, cachedProduct.category, cachedProduct.origin)
        }

        // Try Open Food Facts API first
        do {
            let (name, manufacturer, ingredients, category, _) = try await lookupProductFromOpenFoodFacts(code: code)
            if name != nil {
                let origin = getProductOrigin(code: code)
                return (name, manufacturer, ingredients, category, origin)
            }
        } catch {
            // Continue to next data source
        }

        // Try UPC Database API
        do {
            let (name, manufacturer, ingredients, category, _) = try await lookupProductFromUPCDatabase(code: code)
            if name != nil {
                let origin = getProductOrigin(code: code)
                return (name, manufacturer, ingredients, category, origin)
            }
        } catch {
            // Continue to next data source
        }

        // Try Barcode Lookup API
        do {
            let (name, manufacturer, ingredients, category, _) = try await lookupProductFromBarcodeLookup(code: code)
            if name != nil {
                let origin = getProductOrigin(code: code)
                return (name, manufacturer, ingredients, category, origin)
            }
        } catch {
            // Continue to next data source
        }

        // Try web scraping as a last resort
        do {
            let (name, manufacturer, ingredients, category, _) = try await WebScraper.scrapeProductData(barcode: code)
            if name != nil {
                let origin = getProductOrigin(code: code)
                return (name, manufacturer, ingredients, category, origin)
            }
        } catch {
            // Continue to fallback
        }

        // If all lookups fail, determine origin from barcode prefix and generate detailed info
        let origin = getProductOrigin(code: code)

        // Extract country name from origin (remove emoji)
        let countryName = origin.split(separator: " ").first ?? ""

        // Generate product details based on origin and barcode
        let codeHash = abs(code.hash)

        // Product categories by region
        var productCategories: [String]
        var brandPrefix: String

        // Customize categories and brand prefix based on origin
        if origin.contains("United States") || origin.contains("Canada") {
            productCategories = ["Cereal", "Snack", "Soda", "Candy", "Chips", "Frozen Dinner"]
            brandPrefix = "American"
        } else if origin.contains("China") || origin.contains("Japan") || origin.contains("Korea") {
            productCategories = ["Rice", "Noodles", "Tea", "Sauce", "Snack", "Seafood"]
            brandPrefix = "Asian"
        } else if origin.contains("France") || origin.contains("Italy") || origin.contains("Spain") {
            productCategories = ["Pasta", "Cheese", "Wine", "Olive Oil", "Bread", "Pastry"]
            brandPrefix = "European"
        } else if origin.contains("Australia") || origin.contains("New Zealand") {
            productCategories = ["Meat", "Dairy", "Wine", "Honey", "Fruit", "Cookies"]
            brandPrefix = "Pacific"
        } else if origin.contains("Germany") || origin.contains("Austria") || origin.contains("Switzerland") {
            productCategories = ["Sausage", "Beer", "Bread", "Chocolate", "Cheese", "Pastry"]
            brandPrefix = "Germanic"
        } else if origin.contains("Mexico") || origin.contains("Brazil") || origin.contains("Argentina") {
            productCategories = ["Salsa", "Coffee", "Beans", "Corn", "Fruit", "Spice"]
            brandPrefix = "Latin American"
        } else if origin.contains("India") || origin.contains("Pakistan") || origin.contains("Bangladesh") {
            productCategories = ["Curry", "Rice", "Spice", "Tea", "Lentils", "Snack"]
            brandPrefix = "South Asian"
        } else {
            productCategories = ["Food", "Beverage", "Snack", "Dessert", "Condiment"]
            brandPrefix = "International"
        }

        // Generate product details
        let categoryIndex = codeHash % productCategories.count
        let productType = productCategories[categoryIndex]

        // Create descriptive product name
        let productName = "\(brandPrefix) \(productType) from \(countryName)"

        // Create manufacturer name
        let manufacturerName = "\(countryName) Foods Inc."

        // Create basic ingredients based on product type
        let ingredients = "Ingredients sourced from \(countryName)"

        return (productName, manufacturerName, ingredients, productType, origin)
    }
}
