//
//  ProductComparisonView.swift
//  IsitLocal?
//
//  Created by <PERSON> on 2025-04-02.
//

import SwiftUI
import CoreData

struct ProductComparisonView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @State private var product1: String = ""
    @State private var product2: String = ""
    @State private var comparison: ProductComparison?
    @State private var isLoading = false
    @State private var showingResults = false
    
    private var comparisonManager: ProductComparisonManager {
        return ProductComparisonManager(context: viewContext)
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Form {
                    Section(header: Text("Compare Products")) {
                        TextField("Product 1", text: $product1)
                        TextField("Product 2", text: $product2)
                        
                        Button(action: compareProducts) {
                            Text("Compare")
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.blue)
                                .foregroundColor(.white)
                                .cornerRadius(10)
                        }
                        .disabled(product1.isEmpty || product2.isEmpty || isLoading)
                    }
                    
                    if isLoading {
                        Section {
                            HStack {
                                Spacer()
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                                    .scaleEffect(1.5)
                                    .padding()
                                Spacer()
                            }
                        }
                    }
                    
                    if showingResults, let comparison = comparison {
                        Section(header: Text("Comparison Results")) {
                            VStack(alignment: .leading, spacing: 10) {
                                Text("Product 1: \(comparison.product1Name ?? "Unknown")")
                                    .font(.headline)
                                Text("Origin: \(comparison.product1Origin ?? "Unknown")")
                                Text("Transport Impact: \(String(format: "%.1f", comparison.transportImpact1)) CO2e")
                                
                                Divider()
                                
                                Text("Product 2: \(comparison.product2Name ?? "Unknown")")
                                    .font(.headline)
                                Text("Origin: \(comparison.product2Origin ?? "Unknown")")
                                Text("Transport Impact: \(String(format: "%.1f", comparison.transportImpact2)) CO2e")
                                
                                Divider()
                                
                                let difference = abs(comparison.transportImpact1 - comparison.transportImpact2)
                                let betterProduct = comparison.transportImpact1 < comparison.transportImpact2 ? comparison.product1Name : comparison.product2Name
                                
                                Text("Difference: \(String(format: "%.1f", difference)) CO2e")
                                    .font(.headline)
                                Text("\(betterProduct ?? "Unknown") has a lower environmental impact.")
                                    .foregroundColor(.green)
                                    .fontWeight(.bold)
                            }
                            .padding(.vertical, 5)
                        }
                    }
                }
            }
            .navigationTitle("Product Comparison")
        }
    }
    
    private func compareProducts() {
        guard !product1.isEmpty && !product2.isEmpty else { return }
        
        isLoading = true
        showingResults = false
        
        // Simulate network delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            self.comparison = self.comparisonManager.compareProductsByOrigin(product1: self.product1, product2: self.product2)
            self.isLoading = false
            self.showingResults = true
        }
    }
}
